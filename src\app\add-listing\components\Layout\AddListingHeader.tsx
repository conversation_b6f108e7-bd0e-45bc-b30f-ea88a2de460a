import React from "react";
import Link from "next/link";
import { ArrowLeft, X } from "lucide-react";
import Image from "next/image";
import { useRouter, useParams } from "next/navigation";
import { ADD_LISTING_STEPS } from "../../[[...stepIndex]]/steps";

const AddListingHeader: React.FC = () => {
  const router = useRouter();
  const params = useParams();

  // Step-aware navigation logic (copied from BottomNavigation)
  const stepIndexValue = Array.isArray(params.stepIndex) ? params.stepIndex[0] : params.stepIndex;
  const currentStepIdx = ADD_LISTING_STEPS.findIndex(s => s.key === stepIndexValue);
  const isFirstStep = currentStepIdx === 0;
  const backHref = !isFirstStep ? `/add-listing/${ADD_LISTING_STEPS[currentStepIdx - 1].key}` : "/";

  const handleChevronBack = () => {
    if (!isFirstStep && backHref) {
      router.replace(backHref);
    } else {
      router.replace("/");
    }
  };

  return (
    <header className="add-listing-header">
      <div className="container mx-auto flex h-20 items-center justify-between !px-4 md:!px-8 lg:!px-12 relative">
        {/* Desktop: Logo left, text link right */}
        <div className="hidden md:flex items-center gap-6">
          <Link href="/">
            <Image src="/logos/Brand/Logo_add_listing.png" alt="Logo" width={88} height={32} />
          </Link>
        </div>
        <div className="flex items-center gap-3 ml-auto hidden md:flex border py-2 px-4 border-[#E8EAED] rounded-[4px] hover:border-[#EA580F] transition">
          <Link 
            href="/" 
            className="flex items-center gap-2 text-sm font-medium text-gray-600 hover:text-[#EA580F] transition"
          >
            <ArrowLeft size={16} />
            Retour à l&apos;accueil
          </Link>
        </div>

        {/* Mobile: Back chevron left, X right, no logo */}
        <div className="flex w-full items-center justify-between md:hidden">
          {/* Back chevron */}
          <button
            onClick={handleChevronBack}
            className="flex items-center justify-center w-10 h-10 text-black transition"
            aria-label="Retour"
          >
            <ArrowLeft size={22} className="text-black" />
          </button>
          {/* X button */}
          <Link
            href="/"
            className="flex items-center justify-center w-8 h-8 text-black rounded-full border border-black transition"
            aria-label="Fermer"
          >
            <X size={24} className="font-bold text-black" />
          </Link>
        </div>
      </div>
    </header>
  );
};

export default AddListingHeader;