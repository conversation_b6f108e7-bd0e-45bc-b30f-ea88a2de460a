"use client";

import React from "react";
import CommonLayout from "./CommonLayout";

const PageAddListingIntro = () => {
    return (
        <CommonLayout params={{ stepIndex: "intro" }}>
            <div className="container mx-auto flex flex-col md:flex-row items-center justify-between min-h-[70vh] w-full py-0 md:py-12 gap-8 md:gap-8 xl:gap-32">
                {/* Left column: Headline */}
                <div className="flex-1 max-w-2xl flex items-center md:justify-start w-full md:pl-8 lg:pl-12">
                    <h1 className="font-bold text-gray-900 leading-tight text-left text-[clamp(1.5rem,5vw,2.2rem)] md:text-3xl lg:text-5xl">
                        Commencer sur<br />
                        Almindhar Booking,<br />
                        c&apos;est facile
                    </h1>
                </div>
                {/* Right column: Steps */}
                <div className="flex-1 max-w-xl flex flex-col justify-center w-full">
                    <ol className="space-y-8">
                        <li>
                            <div className="font-bold text-base sm:text-lg md:text-xl lg:text-2xl text-gray-900 mb-1">
                                1. Détails de l&apos;annonce
                            </div>
                            <div className="text-gray-500 text-sm sm:text-base md:text-base lg:text-lg">
                                Racontez ce qui rend votre logement unique : plus vos détails sont complets, plus vous séduirez vos voyageurs !
                            </div>
                        </li>
                        <li>
                            <div className="font-bold text-base sm:text-lg md:text-xl lg:text-2xl text-gray-900 mb-1">
                                2. Tarification et conditions
                            </div>
                            <div className="text-gray-500 text-sm sm:text-base md:text-base lg:text-lg">
                                Fixez vos tarifs et votre politique d&apos;annulation pour inspirer confiance — ne laissez aucun champ vide et maximisez vos revenus !
                            </div>
                        </li>
                        <li>
                            <div className="font-bold text-base sm:text-lg md:text-xl lg:text-2xl text-gray-900 mb-1">
                                3. Disponibilité et informations personnelles
                            </div>
                            <div className="text-gray-500 text-sm sm:text-base md:text-base lg:text-lg">
                                Organisez votre calendrier et complétez vos coordonnées maintenant pour être prêt à accueillir vos prochains hôtes !
                            </div>
                        </li>
                    </ol>
                </div>
            </div>
        </CommonLayout>
    );
};

export default PageAddListingIntro; 