"use client"

import { Fragment, useState } from "react"
import { usePathname } from "next/navigation" // <-- New import for routing
import {
	Dialog,
	DialogPanel,
	Tab,
	TabGroup,
	TabList,
	TabPanel,
	TabPanels,
	Transition,
	TransitionChild,
} from "@headlessui/react"
import { ChevronLeftIcon } from "@heroicons/react/24/outline"
import { XMarkIcon } from "@heroicons/react/24/solid"
import ButtonSubmit from "./ButtonSubmit"
import { useTimeoutFn } from "react-use"
import StaySearchForm from "./(stay-search-form)/StaySearchForm"
import { SearchParams, useSearch } from "@/app/(stay-listings)/SearchContext"
import Image from "next/image";
import logoImage from "@/images/booking_png.png";
import Link from "next/link";

const HeroSearchForm2Mobile = () => {
	const pathname = usePathname(); // Determine current route

	// Existing state declarations
	const [showModal, setShowModal] = useState(false)
	const { searchParams, setSearchParams } = useSearch()
	const [tempSearchParams, setTempSearchParams] = useState(searchParams)
	const [showDialog, setShowDialog] = useState(false)
	const [, , resetIsShowingDialog] = useTimeoutFn(() => setShowDialog(true), 1)

	function closeModal() {
		setShowModal(false)
	}

	function openModal() {
		// Reset temp search params to current search params when opening modal
		setTempSearchParams(searchParams)
		setShowModal(true)
	}

	const handleClearAll = () => {
		const clearedParams: SearchParams = {
			location: "",
			checkIn: null,
			checkOut: null,
			guests: { guestAdults: 0, guestChildren: 0, guestInfants: 0 },
			propertyTypes: [],
			propertyCategories: [],
			amenities: [],
			priceRange: [0, 1000],
			beds: 0,
			bedrooms: 0,
			bathrooms: 0,
			kitchens: 0,
			mapBounds: null,
			paymentType: null,
			roomTypes: [],
			minRating: 0
		}

		// Update temp search params
		setTempSearchParams(clearedParams)

		setShowDialog(false)
		resetIsShowingDialog()
	}

	const handleSearchChange = (newParams: any) => {
		setTempSearchParams(newParams)
	}

	const renderButtonOpenModal = () => {
		return (
			<button
				onClick={openModal}
				className="relative flex items-center space-x-4 rounded-lg border border-neutral-200 px-4 py-2 shadow-lg dark:border-neutral-6000"
			>
				{/* Left arrow icon with click redirect */}
				<ChevronLeftIcon
					className="h-4 w-4 cursor-pointer"
					onClick={(e) => {
						e.stopPropagation() // Prevents triggering openModal
						window.location.href = "/" // Redirects to the home page
					}}
				/>

				{/* Location */}
				<span className="text-sm font-medium">Sousse</span>

				{/* Divider */}
				<span className="h-5 w-px bg-neutral-300 dark:bg-neutral-700" />

				{/* Date range */}
				<span className="text-sm font-medium">Avril 7 – 17</span>

				{/* Divider */}
				<span className="h-5 w-px bg-neutral-300 dark:bg-neutral-700" />

				{/* Travelers */}
				<span className="text-sm font-medium">3 voyageurs</span>
			</button>
		)
	}

	// Conditional rendering based on the current route
	if (pathname === "/listing-stay-map") {
		// Render mobile search form layout for the '/listing-stay-map' route
		return (
			<div className="HeroSearchForm2Mobile">
				{renderButtonOpenModal()}
				<Transition appear show={showModal} as={Fragment}>
					<Dialog as="div" className="HeroSearchFormMobile__Dialog z-max relative" onClose={closeModal}>
						<div className="fixed inset-0 bg-neutral-100 dark:bg-neutral-900">
							<div className="flex h-full">
								<TransitionChild
									as={Fragment}
									enter="ease-out transition-transform"
									enterFrom="opacity-0 translate-y-52"
									enterTo="opacity-100 translate-y-0"
									leave="ease-in transition-transform"
									leaveFrom="opacity-100 translate-y-0"
									leaveTo="opacity-0 translate-y-52"
								>
									<DialogPanel className="w-full">
										{showDialog && (
											<TabGroup manual className="relative flex h-full flex-1 flex-col justify-between overflow-hidden">
												<div className="absolute left-4 top-4">
													<button onClick={closeModal}>
														<XMarkIcon className="h-5 w-5 text-black dark:text-white" />
													</button>
												</div>
												<TabList className="flex w-full justify-center space-x-6 pt-12 text-sm font-semibold text-neutral-500 dark:text-neutral-400 sm:space-x-8 sm:text-base">
													{["Séjour"].map((item, index) => (
														<Tab key={index} as={Fragment}>
															{({ selected }) => (
																<div className="relative select-none outline-none focus:outline-none focus-visible:ring-0">
																	<div className={`${selected ? "text-black dark:text-white" : ""}`}>
																		{item}
																	</div>
																	{selected && (
																		<span className="absolute inset-x-0 top-full border-b-2 border-black dark:border-white"></span>
																	)}
																</div>
															)}
														</Tab>
													))}
												</TabList>
												<div className="flex flex-1 overflow-hidden px-1.5 pt-3 sm:px-4">
													<TabPanels className="hiddenScrollbar flex-1 overflow-y-auto py-4">
														<TabPanel>
															<div className="animate-[myblur_0.4s_ease-in-out] transition-opacity">
																<StaySearchForm onSearchChange={handleSearchChange} />
															</div>
														</TabPanel>
													</TabPanels>
												</div>
												<div className="flex justify-between border-t border-neutral-200 bg-white px-4 py-3 dark:border-neutral-700 dark:bg-neutral-900">
													<button
														type="button"
														className="flex-shrink-0 font-semibold underline"
														onClick={handleClearAll}
													>
														Effacer tout
													</button>
													<ButtonSubmit
														className="bg-booking-orange"
														onSubmit={closeModal}
														searchFilters={tempSearchParams}
													/>
												</div>
											</TabGroup>
										)}
									</DialogPanel>
								</TransitionChild>
							</div>
						</div>
					</Dialog>
				</Transition>
			</div>
		)
	} else {
		// Render normal navbar when not on '/listing-stay-map'
		return (
			<nav className="NormalMobileNavbar bg-white dark:bg-neutral-900">
				<div className="flex items-center justify-between">
					{/* Left placeholder (empty) */}
					<div className="w-1/4" />

					{/* Logo centered */}
					<div className="w-2/5 text-center">
						<Link href="/">
							<Image src={logoImage} alt="logo" className="w-full" priority></Image>
						</Link>
					</div>

					{/* Profile picture on the right */}
					<div className="w-1/4 flex justify-end text-neutral-500 dark:text-neutral-300/90">
						<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
							 stroke="currentColor" aria-hidden="true" data-slot="icon" className="w-6 h-6 ">
							<path strokeLinecap="round" stroke-linejoin="round"
								  d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path>
						</svg>
					</div>
				</div>
			</nav>
		)
	}
}

export default HeroSearchForm2Mobile
