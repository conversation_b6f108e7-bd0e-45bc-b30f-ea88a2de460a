"use client";

import type React from "react";
import { type FC, useState, useEffect, useRef } from "react";
import Input from "@/shared/Input";
import FormItem from "../FormItem";
import { useFormContext } from "../FormContext";
import ConfirmationModal from "@/components/ConfirmationModal";
import FeeModal from "@/app/add-listing/components/FeeModal";
import TimeStayModal, {TimeStayData} from "../components/TimeStayModal";
import CommonLayout from "./CommonLayout";
import Image from "next/image";
import { Plus, Pencil, Trash2, Receipt, Check } from "lucide-react";
import { motion } from "framer-motion";



export type AddPriceProps = {};

const pageVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.5, ease: "easeOut" } },
};

const mainSectionParentVariants = {
    hidden: {},
    visible: { transition: { staggerChildren: 0.1 } },
};

const mainSectionChildVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4, ease: "easeOut" } },
};

const formContentParentVariants = {
    hidden: {},
    visible: { transition: { staggerChildren: 0.1 } },
};

const formContentChildVariants = {
    hidden: { opacity: 0, y: 40 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.35, ease: "easeOut" } },
};

const gridItemParentVariants = {
    hidden: {},
    visible: { transition: { staggerChildren: 0.08 } },
};

const gridItemChildVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { opacity: 1, scale: 1, transition: { duration: 0.3, ease: "easeOut" } },
};


const AddPrice: FC<AddPriceProps> = () => {
    const { formData, setFormData, formErrors, setFormErrors, validateStep } = useFormContext();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [feeToDeleteIndex, setFeeToDeleteIndex] = useState<number | null>(null);
    const [discountToDeleteIndex, setDiscountToDeleteIndex] = useState<number | null>(null);
    
    // Validate on mount and when form data changes
    useEffect(() => {
        // Only validate on mount or if we have actual values to validate
        if (formData.nightlyRate || formData.arrivalTime || formData.departureTime || 
            formData.minStay || formData.maxStay) {
            // Added a check to prevent unnecessary validations if values haven't changed
            const hasChanged = 
                formData.nightlyRate !== undefined ||
                formData.arrivalTime !== undefined ||
                formData.departureTime !== undefined ||
                formData.minStay !== undefined || 
                formData.maxStay !== undefined;
            
            if (hasChanged) {
                validateStep("add-price");
            }
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formData.nightlyRate, formData.arrivalTime, formData.departureTime, 
        formData.minStay, formData.maxStay]);
    
    const [isFeeModalOpen, setIsFeeModalOpen] = useState(false);
    const [feeModalData, setFeeModalData] = useState<{
        title: string;
        price: number;
        feeType: string;
        editingIndex: number | null;
    }>({
        title: "",
        price: 0,
        feeType: "",
        editingIndex: null,
    });
    const [feeTitleError, setFeeTitleError] = useState<string>("");
    const [isTimeStayModalOpen, setIsTimeStayModalOpen] = useState(false);
    const [timeStayData, setTimeStayData] = useState<TimeStayData>({
        arrivalTime: formData.arrivalTime || "",
        departureTime: formData.departureTime || "",
        minStay: formData.minStay || 0,
        maxStay: formData.maxStay || 0,
    });
    const handleNightlyRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = Number.parseFloat(e.target.value);
        setFormData((prev) => ({ ...prev, nightlyRate: isNaN(value) ? undefined : value }));
        // Only validate if we have a valid number input
        if (!isNaN(value)) {
            validateStep("add-price");
        }
    };
    // --- NEW: Detect if Nettoyage fee exists ---
    const hasNettoyageFee = (formData.additionalFees || []).some(
        fee => fee.title && fee.title.trim().toLowerCase() === 'nettoyage'
    );
    const openFeeModal = (feeTitle: string, index: number | null = null) => {
        // Defensive: Prevent opening Nettoyage modal if already exists and not editing
        if (feeTitle === 'Nettoyage' && hasNettoyageFee && index === null) return;
        if (index !== null) {
            const existingFee = formData.additionalFees?.[index];
            if (!existingFee) return;
            setFeeModalData({
                title: existingFee.title as string,
                price: existingFee.price ?? 0,
                feeType: existingFee.feeType ?? "",
                editingIndex: index,
            });
        } else {
            setFeeModalData({
                title: feeTitle,
                price: 0,
                feeType: "",
                editingIndex: null,
            });
        }
        setIsFeeModalOpen(true);
    };
    const closeFeeModal = () => {
        setIsFeeModalOpen(false);
    };
    const confirmFeeModal = () => {
        // Validate title before confirming
        if (!feeModalData.title || feeModalData.title.trim().length < 2) {
            setFeeTitleError("Le titre du frais est obligatoire et doit contenir au moins 2 caractères.");
            return;
        }
        setFeeTitleError("");
        setFormData((prev) => {
            const currentFees = prev.additionalFees ? [...prev.additionalFees] : [];
            const { title, price, feeType, editingIndex } = feeModalData;
            if (editingIndex === null) {
                currentFees.push({ title, price, feeType });
            } else {
                currentFees[editingIndex] = {
                    ...currentFees[editingIndex],
                    title,
                    price,
                    feeType,
                };
            }
            return { ...prev, additionalFees: currentFees };
        });
        setIsFeeModalOpen(false);
    };
    const closeTimeStayModal = () => {
        setIsTimeStayModalOpen(false);
    };
    const confirmTimeStayModal = () => {
        // Create a copy of current form data to validate
        const newFormData = {
            ...formData,
            arrivalTime: timeStayData.arrivalTime,
            departureTime: timeStayData.departureTime,
            minStay: timeStayData.minStay,
            maxStay: timeStayData.maxStay,
        };
        
        // Update form data
        setFormData(newFormData);
        
        // Close modal first to avoid UI issues
        setIsTimeStayModalOpen(false);
        
        // Then validate with a slight delay to ensure state is updated
        setTimeout(() => {
            validateStep("add-price");
        }, 0);
    };
    const confirmRemoveFee = () => {
        if (feeToDeleteIndex !== null) {
            setFormData((prev) => {
                const newFees = [...(prev.additionalFees || [])];
                newFees.splice(feeToDeleteIndex, 1);
                return { ...prev, additionalFees: newFees };
            });
        }
        setIsModalOpen(false);
        setFeeToDeleteIndex(null);
    };
    // Update handler for min and max stay to ensure proper validation
    const handleMinStayChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = Number(e.target.value);
        if (value > 0) {
            setFormData(prev => ({ ...prev, minStay: value }));
            validateStep("add-price");
        } else {
            setFormData(prev => ({ ...prev, minStay: undefined }));
        }
    }

    const handleMaxStayChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = Number(e.target.value);
        if (value > 0) {
            setFormData(prev => ({ ...prev, maxStay: value }));
            validateStep("add-price");
        } else {
            setFormData(prev => ({ ...prev, maxStay: undefined }));
        }
    }
    return (
        <CommonLayout params={{ stepIndex: "add-price" }}>
            <motion.div 
                className="max-w-3xl mx-auto px-4 py-16"
                variants={pageVariants}
                initial="hidden"
                animate="visible"
            >
                <motion.div
                    variants={mainSectionParentVariants}
                    initial="hidden"
                    animate="visible"
                >
                    {/* Header Section */}
                    <motion.div className="mb-10" variants={mainSectionChildVariants}>
                        <h2 className="text-3xl md:text-4xl 2xl:pt-20 font-bold text-neutral-900 mb-2 leading-tight">
                            Quel est votre tarif de base ?
                        </h2>
                        <span className="block text-base text-neutral-500 mb-2">
                            Fixez le prix par nuitée.
                        </span>
                    </motion.div>
                    {/* Price Inputs Row - Combined with Cautionnement */}
                    <motion.div className="flex flex-col md:flex-row gap-6 mb-10" variants={mainSectionChildVariants}>
                        <div className="flex-1">
                            <label className="block text-base font-semibold text-neutral-900 mb-2" htmlFor="nightlyRate">
                                Prix de base
                            </label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 text-sm">DT</span>
                                </div>
                                <Input
                                    id="nightlyRate"
                                    className="rounded-[8px] border border-neutral-300 w-full h-12 !pl-12 text-base focus:outline-none focus:border-booking-orange transition"
                                    placeholder="100"
                                    type="number"
                                    value={formData.nightlyRate ?? ""}
                                    onChange={handleNightlyRateChange}
                                    required
                                />
                            </div>
                            {formErrors.nightlyRate && (
                                <div className="mt-1 flex items-center justify-center" role="alert" aria-live="assertive">
                                    <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-md">
                                        <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                        </svg>
                                        <span className="text-sm text-red-700 font-medium">{formErrors.nightlyRate}</span>
                                    </div>
                                </div>
                            )}
                        </div>
                        {/* Cautionnement Input now here */}
                        <div className="flex-1">
                            <label className="block text-base font-semibold text-neutral-900 mb-2" htmlFor="cautionnement">
                                Cautionnement
                            </label>
                            <Input
                                id="cautionnement"
                                className="rounded-[8px] border border-neutral-300 w-full h-12 px-4 text-base focus:outline-none focus:border-booking-orange transition"
                                placeholder="Optionnel"
                                type="number"
                                value={formData.securityDeposit ?? ""}
                                onChange={(e) => setFormData(prev => ({ ...prev, securityDeposit: Number.parseFloat(e.target.value) || undefined }))}
                            />
                            {formErrors.securityDeposit && (
                                <div className="mt-1 flex items-center justify-center" role="alert" aria-live="assertive">
                                    <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-md">
                                        <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                        </svg>
                                        <span className="text-sm text-red-700 font-medium">{formErrors.securityDeposit}</span>
                                    </div>
                                </div>
                            )}
                        </div>
                    </motion.div>
                </motion.div>
                {/* Form Content */}
                <motion.div 
                    className="space-y-10" 
                    variants={formContentParentVariants}
                >
                    {/* Frequent Fees */}
                    <motion.div variants={formContentChildVariants}>
                        <FormItem className="mb-0">
                            <label className="text-xl font-bold text-neutral-900 mb-1 block">
                                Y a–t–il des frais supplémentaires (ménage, animaux…) ?
                            </label>
                            <p className="mt-1 text-base text-neutral-500 mb-6">
                                Proposez des services supplémentaires comme le ménage ou les transferts pour valoriser votre hébergement.
                            </p>
                            <div className="flex flex-col md:flex-row gap-4">
                                {/* Nettoyage Fee Button */}
                                <button
                                    type="button"
                                    onClick={() => openFeeModal('Nettoyage')}
                                    className="w-full md:w-auto flex items-center gap-4 px-8 py-3 border border-[#D0D5DD] rounded-[4px] bg-transparent shadow-none focus:outline-none cursor-pointer hover:bg-[#F3F4F6] transition-colors"
                                    disabled={hasNettoyageFee}
                                    title={hasNettoyageFee ? 'Le frais de nettoyage a déjà été ajouté.' : undefined}
                                >
                                    <span className="text-2xl">
                                        <Image src="https://api.almindharbooking.com/storage/v1/object/public/amenities-icons//Cleaning.svg" alt="Nettoyage" width={28} height={28} />
                                    </span>
                                    <span className="font-semibold text-lg text-neutral-900">Nettoyage</span>
                                    <Plus size={20} />
                                </button>
                                {/* Add Fee Button */}
                                <button
                                    type="button"
                                    onClick={() => openFeeModal('')}
                                    className="w-full md:w-auto group flex items-center justify-center px-8 py-3 border border-[#D0D5DD] rounded-[4px] bg-transparent shadow-none focus:outline-none cursor-pointer hover:bg-[#F3F4F6] transition-colors"
                                    aria-label="Ajouter un autre frais"
                                >
                                    <Plus size={20} className="transition-transform duration-200 group-hover:scale-125" />
                                </button>
                            </div>
                        </FormItem>
                    </motion.div>

                    {/* Selected Additional Fees Grid */}
                    {formData.additionalFees && formData.additionalFees.length > 0 && (
                        <motion.div 
                            className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8" 
                            variants={gridItemParentVariants} 
                            initial="hidden" 
                            animate="visible"
                        >
                            {formData.additionalFees.map((fee, index) => (
                                <motion.div
                                    key={index}
                                    className="flex items-start gap-4 p-5 rounded-[8px] border border-[#EA580F] bg-[#EA580F26]"
                                    variants={gridItemChildVariants}
                                >
                                    {/* Icon */}
                                    <div className="flex-shrink-0 w-14 h-14 bg-white rounded-[8px] flex items-center justify-center">
                                        {fee.title === 'Nettoyage' ? (
                                            <Image src="https://api.almindharbooking.com/storage/v1/object/public/amenities-icons//Cleaning.svg" alt={fee.title || "Fee"} width={24} height={24} />
                                        ) : (
                                            <Receipt size={24} strokeWidth={2} className="text-neutral-900" />
                                        )}
                                    </div>
                                    {/* Fee Info */}
                                    <div className="flex-1 flex flex-col justify-center">
                                        <div className="flex items-center gap-2 mb-1">
                                            <span className="font-bold text-lg text-neutral-900">{fee.title || "Frais personnalisé"}</span>
                                        </div>
                                        <ul className="space-y-1 text-base text-neutral-900">
                                            {fee.feeType && <li className="flex items-center gap-2"><span className="inline-block w-2 h-2 rounded-full bg-[#EA580F]"></span>{fee.feeType}</li>}
                                            {fee.price && <li className="flex items-center gap-2"><span className="inline-block w-2 h-2 rounded-full bg-[#EA580F]"></span>{fee.price} TND</li>}
                                        </ul>
                                    </div>
                                    {/* Actions */}
                                    <div className="flex flex-col md:flex-row items-center gap-2 md:gap-3">
                                        <button
                                            type="button"
                                            onClick={() => openFeeModal("", index)}
                                            className="text-neutral-700 hover:text-[#EA580F] p-1"
                                            aria-label="Modifier"
                                        >
                                            <Pencil size={20} />
                                        </button>
                                        <button
                                            type="button"
                                            onClick={() => { setFeeToDeleteIndex(index); setIsModalOpen(true); }}
                                            className="text-neutral-700 hover:text-red-500 p-1"
                                            aria-label="Supprimer"
                                        >
                                            <Trash2 size={20} />
                                        </button>
                                    </div>
                                </motion.div>
                            ))}
                        </motion.div>
                    )}

                    {/* New Stay Organization Section */}
                    <motion.div className="mt-12" variants={formContentChildVariants}>
                        <h2 className="text-xl font-bold text-neutral-900 mb-6">
                            Comment souhaitez-vous organiser les séjours chez vous ?
                        </h2>
                        {/* Min/Max stay on same row */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-6">
                            <div>
                                <label className="block font-semibold text-base mb-2">Durée minimale du séjour (nuits)</label>
                                <input
                                    type="number"
                                    min={1}
                                    className="w-full rounded-[8px] border border-neutral-300 h-12 px-4 text-base focus:outline-none focus:border-booking-orange transition"
                                    value={formData.minStay || ''}
                                    onChange={handleMinStayChange}
                                />
                                {formErrors.minStay && (
                                    <div className="mt-1 flex items-center justify-center" role="alert" aria-live="assertive">
                                        <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-md">
                                            <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                                <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                            </svg>
                                            <span className="text-sm text-red-700 font-medium">{formErrors.minStay}</span>
                                        </div>
                                    </div>
                                )}
                            </div>
                            <div>
                                <label className="block font-semibold text-base mb-2">Durée maximale du séjour (nuits)</label>
                                <input
                                    type="number"
                                    min={1}
                                    className="w-full rounded-[8px] border border-neutral-300 h-12 px-4 text-base focus:outline-none focus:border-booking-orange transition"
                                    value={formData.maxStay || ''}
                                    onChange={handleMaxStayChange}
                                />
                                {formErrors.maxStay && (
                                    <div className="mt-1 flex items-center justify-center" role="alert" aria-live="assertive">
                                        <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-md">
                                            <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                                <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                            </svg>
                                            <span className="text-sm text-red-700 font-medium">{formErrors.maxStay}</span>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                        {/* Arrival/Departure on same row */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div>
                                <label className="block font-semibold text-base mb-2">Arrivée</label>
                                <ArrivalTimeDropdown
                                    mode={formData.arrivalMode || 'single'}
                                    time={formData.arrivalTime || ''}
                                    timeStart={formData.arrivalTimeStart || ''}
                                    timeEnd={formData.arrivalTimeEnd || ''}
                                    onModeChange={mode => setFormData(prev => ({ ...prev, arrivalMode: mode }))}
                                    onTimeChange={val => setFormData(prev => ({ ...prev, arrivalTime: val }))}
                                    onRangeChange={(start, end) => setFormData(prev => ({ ...prev, arrivalTimeStart: start, arrivalTimeEnd: end }))}
                                />
                                {formErrors.arrivalTime && (
                                    <div className="mt-1 flex items-center justify-center" role="alert" aria-live="assertive">
                                        <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-md">
                                            <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                                <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                            </svg>
                                            <span className="text-sm text-red-700 font-medium">{formErrors.arrivalTime}</span>
                                        </div>
                                    </div>
                                )}
                            </div>
                            <div>
                                <label className="block font-semibold text-base mb-2">Départ</label>
                                <TimeDropdown
                                    value={formData.departureTime || ''}
                                    onChange={val => setFormData(prev => ({ ...prev, departureTime: val }))}
                                />
                                {formErrors.departureTime && (
                                    <div className="mt-1 flex items-center justify-center" role="alert" aria-live="assertive">
                                        <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-md">
                                            <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                                <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                            </svg>
                                            <span className="text-sm text-red-700 font-medium">{formErrors.departureTime}</span>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </motion.div>
                </motion.div>
                {/* Modals */}
                {isFeeModalOpen && (
                    <FeeModal
                        open={isFeeModalOpen}
                        feeModalData={feeModalData}
                        setFeeModalData={setFeeModalData}
                        onConfirm={confirmFeeModal}
                        onClose={closeFeeModal}
                        titleEditable={feeModalData.title !== 'Nettoyage' && (feeModalData.editingIndex === null || formData.additionalFees?.[feeModalData.editingIndex]?.title !== 'Nettoyage')}
                        feeTitleError={feeTitleError}
                    />
                )}
                {isTimeStayModalOpen && (
                    <TimeStayModal
                        open={isTimeStayModalOpen}
                        timeStayData={timeStayData}
                        setTimeStayData={setTimeStayData}
                        onConfirm={confirmTimeStayModal}
                        onClose={closeTimeStayModal}
                    />
                )}
                <ConfirmationModal
                    isOpen={isModalOpen}
                    onClose={() => {
                        setIsModalOpen(false);
                        setFeeToDeleteIndex(null);
                        setDiscountToDeleteIndex(null);
                    }}
                    onConfirm={confirmRemoveFee}
                    title="Confirmer la suppression"
                    message={
                        feeToDeleteIndex !== null
                            ? "Êtes-vous sûr de vouloir supprimer ce frais supplémentaire ?"
                            : "Êtes-vous sûr de vouloir supprimer cette réduction ?"
                    }
                />
            </motion.div>
        </CommonLayout>
    );
};

function TimeDropdown({ value, onChange, className = "" }: { value: string; onChange: (val: string) => void; className?: string }): React.ReactElement {
    const [open, setOpen] = useState(false);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const listRef = useRef<HTMLUListElement>(null);
    const [highlighted, setHighlighted] = useState<number>(-1);
    const [dropdownWidth, setDropdownWidth] = useState<number | undefined>(undefined);
    // 30-minute increments
    const timeOptions = Array.from({ length: 24 * 2 }, (_, i) => {
        const hour = Math.floor(i / 2);
        const minute = (i % 2) * 30;
        return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    });

    useEffect(() => {
        if (open && buttonRef.current) {
            const rect = buttonRef.current.getBoundingClientRect();
            setDropdownWidth(rect.width);
        }
    }, [open]);

    // Click-away-to-close logic
    useEffect(() => {
        if (!open) return;
        function handleClick(event: MouseEvent) {
            const target = event.target as Node;
            if (
                buttonRef.current && !buttonRef.current.contains(target) &&
                listRef.current && !listRef.current.contains(target)
            ) {
                setOpen(false);
            }
        }
        document.addEventListener('mousedown', handleClick);
        return () => document.removeEventListener('mousedown', handleClick);
    }, [open]);

    const handleSelect = (opt: string) => {
        onChange(opt);
        setOpen(false);
        setHighlighted(-1);
        buttonRef.current?.focus();
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (!open) {
            if (e.key === 'ArrowDown' || e.key === 'Enter' || e.key === ' ') {
                setOpen(true);
                setHighlighted(0);
                e.preventDefault();
            }
            return;
        }
        if (e.key === 'ArrowDown') {
            setHighlighted(h => Math.min(h + 1, timeOptions.length - 1));
            e.preventDefault();
        } else if (e.key === 'ArrowUp') {
            setHighlighted(h => Math.max(h - 1, 0));
            e.preventDefault();
        } else if (e.key === 'Enter' || e.key === ' ') {
            if (highlighted >= 0) handleSelect(timeOptions[highlighted]);
            e.preventDefault();
        } else if (e.key === 'Escape') {
            setOpen(false);
            setHighlighted(-1);
            buttonRef.current?.focus();
            e.preventDefault();
        }
    };

    return (
        <div className={`relative ${className}`} tabIndex={-1}>
            <button
                ref={buttonRef}
                type="button"
                className="w-full rounded-[8px] border border-neutral-300 h-12 px-4 text-base text-left bg-white text-neutral-900 focus:outline-none focus:border-[#EA580F] transition flex items-center justify-between"
                aria-haspopup="listbox"
                aria-expanded={open}
                onClick={() => setOpen(o => !o)}
                onKeyDown={handleKeyDown}
            >
                <span>{value || "--:-- --"}</span>
                <svg className="w-4 h-4 ml-2 text-[#EA580F]" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
            </button>
            {open && (
                <ul
                    ref={listRef}
                    tabIndex={-1}
                    role="listbox"
                    className="absolute z-50 max-h-60 overflow-auto rounded-[8px] border border-neutral-200 bg-white shadow-lg"
                    style={{
                        width: dropdownWidth,
                        left: 0,
                        bottom: '100%',
                        marginBottom: '0.5rem',
                    }}
                    onKeyDown={handleKeyDown}
                >
                    {timeOptions.map((opt, i) => {
                        const isSelected = value === opt;
                        const isHighlighted = highlighted === i;
                        return (
                            <li
                                key={opt}
                                role="option"
                                aria-selected={isSelected}
                                className={`px-4 py-2 cursor-pointer select-none transition-colors text-base ${isSelected ? 'bg-[#EA580F] text-white' : isHighlighted ? 'bg-[#EA580F26] text-[#EA580F]' : 'text-neutral-900'} ${isHighlighted ? 'font-semibold' : ''}`}
                                onMouseEnter={() => setHighlighted(i)}
                                onMouseLeave={() => setHighlighted(-1)}
                                onClick={() => handleSelect(opt)}
                            >
                                {opt}
                            </li>
                        );
                    })}
                </ul>
            )}
        </div>
    );
}

// Custom rectangular radio for brand-aligned, accessible toggles
function RectRadio({ checked, onChange, name, value, ariaLabel, className = "" }: {
    checked: boolean;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    name: string;
    value: string;
    ariaLabel?: string;
    className?: string;
}) {
    return (
        <label className={`inline-flex items-center cursor-pointer group ${className}`}> 
            <input
                type="radio"
                checked={checked}
                onChange={onChange}
                name={name}
                value={value}
                aria-label={ariaLabel}
                className="sr-only"
            />
            <span
                className={`w-5 h-5 flex items-center justify-center rounded-[4px] border transition-colors duration-150
                    ${checked ? 'bg-[#EA580F] border-[#EA580F]' : 'bg-white border-neutral-300'}
                    group-focus-within:ring-2 group-focus-within:ring-[#EA580F] group-focus-within:ring-offset-2
                `}
                tabIndex={-1}
                aria-hidden="true"
            >
                {checked && <Check size={16} color="#fff" strokeWidth={3} />}
            </span>
        </label>
    );
}

// Reusable custom time dropdown for both single and range pickers
function TimeSelectDropdown({ value, onChange, label, className = "" }: {
    value: string;
    onChange: (val: string) => void;
    label?: string;
    className?: string;
}) {
    const [open, setOpen] = useState(false);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const listRef = useRef<HTMLUListElement>(null);
    const [highlighted, setHighlighted] = useState<number>(-1);
    const [dropdownWidth, setDropdownWidth] = useState<number | undefined>(undefined);
    const [openDirection, setOpenDirection] = useState<'down' | 'up'>('down');
    const DROPDOWN_HEIGHT = 260; // px, must match max-h-60 (15*16=240) or actual dropdown height
    // 30-minute increments
    const timeOptions = Array.from({ length: 24 * 2 }, (_, i) => {
        const hour = Math.floor(i / 2);
        const minute = (i % 2) * 30;
        return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    });

    // Click-away-to-close logic
    useEffect(() => {
        if (!open) return;
        function handleClick(event: MouseEvent) {
            const target = event.target as Node;
            if (
                buttonRef.current && !buttonRef.current.contains(target) &&
                listRef.current && !listRef.current.contains(target)
            ) {
                setOpen(false);
            }
        }
        document.addEventListener('mousedown', handleClick);
        return () => document.removeEventListener('mousedown', handleClick);
    }, [open]);

    // On open, measure input width and viewport position, and set dropdown direction
    useEffect(() => {
        if (open && buttonRef.current) {
            const rect = buttonRef.current.getBoundingClientRect();
            setDropdownWidth(rect.width);
            // Check available space below and above
            const spaceBelow = window.innerHeight - rect.bottom;
            const spaceAbove = rect.top;
            // Use constant for dropdown height for reliable flip logic
            if (spaceBelow < DROPDOWN_HEIGHT && spaceAbove > spaceBelow) {
                setOpenDirection('up');
            } else {
                setOpenDirection('down');
            }
        }
    }, [open]);

    const handleSelect = (opt: string) => {
        onChange(opt);
        setOpen(false);
        setHighlighted(-1);
        buttonRef.current?.focus();
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (!open) {
            if (e.key === 'ArrowDown' || e.key === 'Enter' || e.key === ' ') {
                setOpen(true);
                setHighlighted(0);
                e.preventDefault();
            }
            return;
        }
        if (e.key === 'ArrowDown') {
            setHighlighted(h => Math.min(h + 1, timeOptions.length - 1));
            e.preventDefault();
        } else if (e.key === 'ArrowUp') {
            setHighlighted(h => Math.max(h - 1, 0));
            e.preventDefault();
        } else if (e.key === 'Enter' || e.key === ' ') {
            if (highlighted >= 0) handleSelect(timeOptions[highlighted]);
            e.preventDefault();
        } else if (e.key === 'Escape') {
            setOpen(false);
            setHighlighted(-1);
            buttonRef.current?.focus();
            e.preventDefault();
        }
    };

    return (
        <div className={`relative ${className}`} tabIndex={-1}>
            <button
                ref={buttonRef}
                type="button"
                className="w-full rounded-[8px] border border-neutral-300 h-12 px-4 text-base text-left bg-white text-neutral-900 focus:outline-none focus:border-[#EA580F] transition flex items-center justify-between"
                aria-haspopup="listbox"
                aria-expanded={open}
                onClick={() => setOpen(o => !o)}
                onKeyDown={handleKeyDown}
            >
                <span>{value || "--:-- --"}</span>
                <svg className="w-4 h-4 ml-2 text-[#EA580F]" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
            </button>
            {open && (
                <ul
                    ref={listRef}
                    tabIndex={-1}
                    role="listbox"
                    className={`absolute z-50 mt-1 max-h-60 overflow-auto rounded-[8px] border border-neutral-200 bg-white shadow-lg`}
                    style={{
                        width: dropdownWidth,
                        left: 0,
                        [openDirection === 'up' ? 'bottom' : 'top']: '100%',
                        marginBottom: openDirection === 'up' ? '0.5rem' : undefined,
                        marginTop: openDirection === 'down' ? '0.5rem' : undefined,
                    }}
                >
                    {timeOptions.map((opt, i) => {
                        const isSelected = value === opt;
                        const isHighlighted = highlighted === i;
                        return (
                            <li
                                key={opt}
                                role="option"
                                aria-selected={isSelected}
                                className={`px-4 py-2 cursor-pointer select-none transition-colors text-base ${isSelected ? 'bg-[#EA580F] text-white' : isHighlighted ? 'bg-[#EA580F26] text-[#EA580F]' : 'text-neutral-900'} ${isHighlighted ? 'font-semibold' : ''}`}
                                onMouseEnter={() => setHighlighted(i)}
                                onMouseLeave={() => setHighlighted(-1)}
                                onClick={() => handleSelect(opt)}
                            >
                                {opt}
                            </li>
                        );
                    })}
                </ul>
            )}
        </div>
    );
}

function ArrivalTimeDropdown({
    mode, time, timeStart, timeEnd, onModeChange, onTimeChange, onRangeChange, className = ""
}: {
    mode: 'single' | 'range';
    time: string;
    timeStart: string;
    timeEnd: string;
    onModeChange: (mode: 'single' | 'range') => void;
    onTimeChange: (val: string) => void;
    onRangeChange: (start: string, end: string) => void;
    className?: string;
}): React.ReactElement {
    // For range selection
    const [rangeStart, setRangeStart] = useState(timeStart || "");
    const [rangeEnd, setRangeEnd] = useState(timeEnd || "");
    const [dropdownWidth, setDropdownWidth] = useState<number | undefined>(undefined);
    const [open, setOpen] = useState(false);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const [highlighted, setHighlighted] = useState<number>(-1);
    const [openDirection, setOpenDirection] = useState<'down' | 'up'>('down');
    const timeOptions = Array.from({ length: 24 * 2 }, (_, i) => {
        const hour = Math.floor(i / 2);
        const minute = (i % 2) * 30;
        return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    });

    useEffect(() => {
        setRangeStart(timeStart || "");
        setRangeEnd(timeEnd || "");
    }, [timeStart, timeEnd]);

    // Measure input width and viewport position on open
    useEffect(() => {
        if (open && buttonRef.current) {
            const rect = buttonRef.current.getBoundingClientRect();
            setDropdownWidth(rect.width);
            // Check available space below and above
            const spaceBelow = window.innerHeight - rect.bottom;
            const spaceAbove = rect.top;
            // Assume dropdown height ~320px (toggle + list)
            if (spaceBelow < 320 && spaceAbove > spaceBelow) {
                setOpenDirection('up');
            } else {
                setOpenDirection('down');
            }
        }
    }, [open]);

    // Click-away-to-close logic for ArrivalTimeDropdown
    useEffect(() => {
        if (!open) return;
        function handleClick(event: MouseEvent) {
            const target = event.target as Node;
            if (
                buttonRef.current && !buttonRef.current.contains(target) &&
                dropdownRef.current && !dropdownRef.current.contains(target)
            ) {
                setOpen(false);
            }
        }
        document.addEventListener('mousedown', handleClick);
        return () => document.removeEventListener('mousedown', handleClick);
    }, [open]);

    const handleSelect = (val: string) => {
        onTimeChange(val);
        setOpen(false);
        setHighlighted(-1);
        buttonRef.current?.focus();
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (!open) {
            if (e.key === 'ArrowDown' || e.key === 'Enter' || e.key === ' ') {
                setOpen(true);
                setHighlighted(0);
                e.preventDefault();
            }
            return;
        }
        if (e.key === 'ArrowDown') {
            setHighlighted(h => Math.min(h + 1, timeOptions.length - 1));
            e.preventDefault();
        } else if (e.key === 'ArrowUp') {
            setHighlighted(h => Math.max(h - 1, 0));
            e.preventDefault();
        } else if (e.key === 'Enter' || e.key === ' ') {
            if (highlighted >= 0) handleSelect(timeOptions[highlighted]);
            e.preventDefault();
        } else if (e.key === 'Escape') {
            setOpen(false);
            setHighlighted(-1);
            buttonRef.current?.focus();
            e.preventDefault();
        }
    };

    const displayValue = mode === 'range' && rangeStart && rangeEnd
        ? `${rangeStart} - ${rangeEnd}`
        : (mode === 'single' && time ? time : "--:-- --");

    return (
        <div className={`relative ${className}`} tabIndex={-1}>
            <button
                ref={buttonRef}
                type="button"
                className="w-full rounded-[8px] border border-neutral-300 h-12 px-4 text-base text-left bg-white text-neutral-900 focus:outline-none focus:border-[#EA580F] transition flex items-center justify-between"
                aria-haspopup="dialog"
                aria-expanded={open}
                onClick={() => setOpen(o => !o)}
                onKeyDown={handleKeyDown}
            >
                <span>{displayValue}</span>
                <svg className="w-4 h-4 ml-2 text-[#EA580F]" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
            </button>
            {open && (
                <div
                    ref={dropdownRef}
                    className="absolute z-50 max-h-[320px] overflow-visible rounded-[8px] border border-neutral-200 bg-white shadow-lg p-4"
                    style={{
                        width: dropdownWidth,
                        left: 0,
                        [openDirection === 'up' ? 'bottom' : 'top']: '100%',
                        marginBottom: openDirection === 'up' ? '0.5rem' : undefined,
                        marginTop: openDirection === 'down' ? '0.5rem' : undefined,
                    }}
                >
                    <div className="flex items-center gap-4 mb-4">
                        <RectRadio
                            checked={mode === 'single'}
                            onChange={() => onModeChange('single')}
                            name="arrival-mode"
                            value="single"
                            ariaLabel="Heure unique"
                        />
                        <span className="select-none">Heure unique</span>
                        <RectRadio
                            checked={mode === 'range'}
                            onChange={() => onModeChange('range')}
                            name="arrival-mode"
                            value="range"
                            ariaLabel="Plage horaire"
                        />
                        <span className=" select-none">Plage horaire</span>
                    </div>
                    {mode === 'single' ? (
                        <ul className="max-h-48 overflow-auto rounded-[8px] border border-neutral-100">
                            {timeOptions.map(opt => (
                                <li
                                    key={opt}
                                    className={`px-4 py-2 cursor-pointer select-none transition-colors text-base ${time === opt ? 'bg-[#EA580F] text-white' : 'hover:bg-[#EA580F26] hover:text-[#EA580F] text-neutral-900'}`}
                                    onClick={() => handleSelect(opt)}
                                >
                                    {opt}
                                </li>
                            ))}
                        </ul>
                    ) : (
                        <div>
                            <div className="flex gap-2 mb-4">
                                <TimeSelectDropdown
                                    value={rangeStart}
                                    onChange={setRangeStart}
                                    label="Heure de début"
                                    className="w-1/2"
                                />
                                <span className="self-center">—</span>
                                <TimeSelectDropdown
                                    value={rangeEnd}
                                    onChange={setRangeEnd}
                                    label="Heure de fin"
                                    className="w-1/2"
                                />
                            </div>
                            <button
                                type="button"
                                className="w-full bg-[#EA580F] text-white rounded-[8px] py-2 font-semibold hover:bg-[#d65a0f] transition"
                                disabled={!rangeStart || !rangeEnd}
                                onClick={() => { onRangeChange(rangeStart, rangeEnd); setOpen(false); }}
                            >
                                Valider la plage horaire
                            </button>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
}

export default AddPrice; 