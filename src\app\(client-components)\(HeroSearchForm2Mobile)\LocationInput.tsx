"use client";

import { MapPinIcon, MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { useState, useEffect, useRef, type FC } from "react";
import useOutsideAlerter from "@/hooks/useOutsideAlerter";

interface Props {
    className?: string;
    value?: string; // Controlled value from parent
    headingText?: string;
    onChange: (value: string) => void; // Called on every text change
    onSelect?: (value: string) => void; // Called only when a suggestion is selected
}

const LocationInput: FC<Props> = ({
                                      className = "",
                                      value = "",
                                      headingText = "Où aller?",
                                      onChange,
                                      onSelect,
                                  }) => {
    const [inputValue, setInputValue] = useState(value);
    const [showPopover, setShowPopover] = useState(false);
    const [suggestions, setSuggestions] = useState<google.maps.places.AutocompletePrediction[]>([]);
    const [recentSearches, setRecentSearches] = useState<string[]>([
        "Tunis, Tunisia",
        "Sousse, Tunisia",
        "Sfax, Tunisia",
        "Hammamet, Tunisia",
    ]);
    const containerRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    // Synchronize internal state when parent's value changes externally
    useEffect(() => {
        if (value !== inputValue) {
            setInputValue(value);
        }
    }, [value]);

    useOutsideAlerter(containerRef, () => {
        setShowPopover(false);
    });

    useEffect(() => {
        if (showPopover && inputRef.current) {
            inputRef.current.focus();
        }
    }, [showPopover]);

    useEffect(() => {
        if (inputValue && window.google && window.google.maps) {
            const service = new window.google.maps.places.AutocompleteService();
            const sessionToken = new window.google.maps.places.AutocompleteSessionToken();
            service.getPlacePredictions(
                {
                    input: inputValue,
                    sessionToken: sessionToken,
                    componentRestrictions: { country: "tn" },
                    types: ["geocode"],
                },
                (predictions, status) => {
                    if (status === window.google.maps.places.PlacesServiceStatus.OK && predictions) {
                        setSuggestions(predictions);
                    } else {
                        setSuggestions([]);
                    }
                }
            );
        } else {
            setSuggestions([]);
        }
    }, [inputValue]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;
        setInputValue(newValue);
        onChange(newValue); // Notify parent of typing changes only
        setShowPopover(true);
    };

    const handleInputFocus = () => {
        setShowPopover(true);
        setSuggestions([]);
    };

    const handleSelectLocation = (item: google.maps.places.AutocompletePrediction | string) => {
        const selectedValue = typeof item === "string" ? item : item.description;
        setInputValue(selectedValue);
        onChange(selectedValue);
        // Call onSelect if provided, to signal that a suggestion has been chosen.
        if (onSelect) {
            onSelect(selectedValue);
        }
        setShowPopover(false);
        setRecentSearches((prev) => [selectedValue, ...prev.filter((i) => i !== selectedValue)].slice(0, 4));
    };

    const renderSearchValues = (items: google.maps.places.AutocompletePrediction[] | string[]) => {
        return (
            <div className="mt-3">
                {items.map((item) => {
                    const itemText = typeof item === "string" ? item : item.description;
                    return (
                        <div
                            className="py-2 mb-1 flex items-center space-x-3 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                            onClick={() => handleSelectLocation(item)}
                            key={typeof item === "string" ? item : item.place_id}
                        >
                            <MapPinIcon className="w-5 h-5 text-neutral-500 dark:text-neutral-400" />
                            <span>{itemText}</span>
                        </div>
                    );
                })}
            </div>
        );
    };

    return (
        <div className={`${className}`} ref={containerRef}>
            <div className="p-5">
                <span className="block font-semibold text-xl sm:text-2xl">{headingText}</span>
                <div className="relative mt-5">
                    <input
                        className="block w-full bg-transparent border px-4 py-3 pr-12 border-neutral-900 dark:border-neutral-200 rounded-xl focus:ring-0 focus:outline-none text-base leading-none placeholder-neutral-500 dark:placeholder-neutral-300 truncate font-bold placeholder:truncate"
                        placeholder="Search destinations"
                        value={inputValue}
                        onChange={handleInputChange}
                        onFocus={handleInputFocus}
                        ref={inputRef}
                    />
                    <span className="absolute right-2.5 top-1/2 -translate-y-1/2">
            <MagnifyingGlassIcon className="w-5 h-5 text-neutral-700 dark:text-neutral-400" />
          </span>
                </div>
                {showPopover && (
                    <div className="mt-7 bg-white dark:bg-gray-800">
                        <p className="block font-semibold text-base px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                            {inputValue && suggestions.length > 0 ? "Suggestions" : "Destinations Populaires"}
                        </p>
                        {renderSearchValues(inputValue && suggestions.length > 0 ? suggestions : recentSearches)}
                    </div>
                )}
            </div>
        </div>
    );
};

export default LocationInput;
