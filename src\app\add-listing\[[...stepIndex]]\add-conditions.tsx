"use client";

import React, { useState, useEffect } from "react";
import { Dialog, DialogPanel, DialogTitle } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { CheckIcon } from '@heroicons/react/24/solid';
import { useFormContext } from "@/app/add-listing/FormContext";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import CancellationPolicyTimeline from "./components/CancellationPolicyTimeline";
import CommonLayout from "./CommonLayout";
import { motion, AnimatePresence } from "framer-motion";

// Define the CancellationPolicy type
interface CancellationPolicy {
  id: string;
  policy_name: string;
  policy_descrition: string;
}

const pageVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 0.5, ease: "easeOut" } },
};

const mainContentParentVariants = {
  hidden: {},
  visible: { transition: { staggerChildren: 0.1 } },
};

const mainContentChildVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.4, ease: "easeOut" } },
};

const modalListParentVariants = {
  hidden: {},
  visible: { transition: { staggerChildren: 0.08 } },
};

const modalListItemVariants = {
  hidden: { opacity: 0, y: 60 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.08, ease: "circOut" } },
};

const mobileModalVariants = {
  hidden: { y: '100%', opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 500,
      damping: 35,
      mass: 0.8
    }
  },
  exit: {
    y: '100%',
    opacity: 0,
    transition: {
      type: "spring",
      stiffness: 500,
      damping: 35,
      mass: 0.8
    }
  },
};

const AddConditions: React.FC = () => {
  const { formData, setFormData, formErrors, setFormErrors, validateStep } = useFormContext();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [policies, setPolicies] = useState<CancellationPolicy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Initialize state from formData or with defaults
  const [selectedPolicyId, setSelectedPolicyId] = useState<string>(formData.cancellationPolicyId || "");
  // Add a temporary draft selection for the modal
  const [draftPolicyId, setDraftPolicyId] = useState<string>("");

  // Fetch cancellation policies on component mount
  useEffect(() => {
    const fetchPolicies = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/cancellation-policies');

        if (!response.ok) {
          throw new Error('Failed to fetch cancellation policies');
        }

        const data = await response.json();
        setPolicies(data);

        // If we have policies and no selected policy yet, select the first one
        if (data.length > 0 && !selectedPolicyId) {
          setSelectedPolicyId(data[0].id);
          // Update formData with the default selected policy
          setFormData(prev => ({
            ...prev,
            cancellationPolicyId: data[0].id
          }));
          // Do NOT validate here; validation should only happen on navigation
        }
      } catch (err) {
        console.error('Error fetching cancellation policies:', err);
        setError('Failed to load cancellation policies');
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load cancellation policies. Please reload the page."
        });
      } finally {
        setLoading(false);
      }
    };

    fetchPolicies();
    // Do NOT validate on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const openModal = () => {
    // Initialize draft selection with current selection
    setDraftPolicyId(selectedPolicyId);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    // Discard draft changes when closing without saving
    setDraftPolicyId(selectedPolicyId);
    setIsModalOpen(false);
  };

  const handlePolicySelect = (policyId: string) => {
    // Only update the draft selection
    setDraftPolicyId(policyId);
    // Optionally clear the error for cancellationPolicyId for better UX
    setFormErrors(prev => {
      const { cancellationPolicyId, ...rest } = prev;
      return rest;
    });
  };

  const savePolicy = () => {
    // Update the real selection from draft
    setSelectedPolicyId(draftPolicyId);
    // Update the form data with the selected policy
    setFormData(prev => ({
      ...prev,
      cancellationPolicyId: draftPolicyId
    }));
    // Validate the step after setting the policy
    validateStep("add-conditions");
    closeModal();
  };

  // Find the selected policy details
  const selectedPolicy = policies.find(p => p.id === selectedPolicyId);

  // Display loading state
  if (loading) {
    return (
      <CommonLayout params={{ stepIndex: "add-conditions" }}>
        <motion.div
          className="max-w-3xl mx-auto px-4 py-16"
          variants={pageVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-booking-orange" />
            <span className="ml-2 text-gray-600">Chargement des conditions d&apos;annulation...</span>
          </div>
        </motion.div>
      </CommonLayout>
    );
  }

  // Display error state
  if (error) {
    return (
      <CommonLayout params={{ stepIndex: "add-conditions" }}>
        <motion.div
          className="max-w-3xl mx-auto px-4 py-16"
          variants={pageVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="p-8 text-center">
            <div className="text-red-500 mb-2">Erreur: {error}</div>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-booking-orange text-white rounded-lg"
            >
              Réessayer
            </button>
          </div>
        </motion.div>
      </CommonLayout>
    );
  }

  return (
    <CommonLayout params={{ stepIndex: "add-conditions" }}>
      <motion.div
        className="max-w-3xl mx-auto px-4 py-16 "
        variants={pageVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div
          className="space-y-6"
          variants={mainContentParentVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.h1
            className="text-xl sm:text-2xl font-semibold text-gray-900 leading-tight"
            variants={mainContentChildVariants}
          >
            Quelle politique d&apos;annulation vous convient le mieux ?
          </motion.h1>

          <motion.div variants={mainContentChildVariants}>
            <div className="mb-6">
              <p className="text-sm text-gray-600 mt-1">
                Définissez votre politique d&apos;annulation.
              </p>
            </div>
          </motion.div>

          <motion.div className="border-t border-gray-200 my-6" variants={mainContentChildVariants}></motion.div>

          <motion.div className="border border-gray-200 rounded-lg p-4 mb-4" variants={mainContentChildVariants}>
            <div className="flex justify-between items-center">
              <h3 className="font-semibold text-lg">{selectedPolicy?.policy_name || "Sélectionnez une politique"}</h3>
              <button
                className="px-3 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium text-booking-orange border border-booking-orange rounded-[2px] sm:rounded-lg hover:bg-booking-orange hover:text-white active:bg-opacity-90 transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-booking-orange focus-visible:ring-offset-2"
                onClick={openModal}
              >
                Modifier
              </button>
            </div>
            {selectedPolicy?.policy_descrition ? (
              <ul className="list-disc list-inside text-sm text-gray-600 mt-2 space-y-1 leading-relaxed">
                {selectedPolicy.policy_descrition.split('\n').map((line, index) => {
                  const cleanedLine = line.trim().replace(/^- /g, '');
                  return cleanedLine && <li key={index}>{cleanedLine}</li>;
                })}
              </ul>
            ) : (
              <p className="text-sm text-gray-600 mt-1 leading-relaxed">
                Veuillez sélectionner une politique d&apos;annulation
              </p>
            )}
          </motion.div>

          {/* Display validation error if any */}
          {formErrors.cancellationPolicyId && (
            <div className="mt-2 text-red-500 text-sm">
              {formErrors.cancellationPolicyId}
            </div>
          )}

          {selectedPolicy && (
            <motion.div
              className="mt-8 border border-gray-200 rounded-lg p-6"
              variants={mainContentChildVariants}
              initial="hidden"
              animate="visible"
            >
              <CancellationPolicyTimeline policyType={selectedPolicy.policy_name} />
            </motion.div>
          )}
        </motion.div>

        {/* Cancellation Policy Selection Modal */}
        <AnimatePresence>
          {isModalOpen && (
            <Dialog as="div" className="relative z-50" open={isModalOpen} onClose={closeModal}>
              <motion.div
                className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2, ease: "easeOut" }}
              />

              <div className="fixed inset-0 overflow-y-auto">
                <div className="flex min-h-full sm:items-center sm:justify-center text-left sm:p-0">
                  <DialogPanel
                    as={motion.div}
                    variants={mobileModalVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    className="fixed bottom-0 left-0 w-full transform overflow-hidden bg-white text-left align-middle shadow-xl sm:relative sm:max-w-lg sm:rounded-lg"
                  >
                    <div className="relative max-h-[80vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center">
                        <DialogTitle
                          as="h3"
                          className="text-xl font-medium text-gray-900 text-left"
                        >
                          Choisissez vos conditions standard
                        </DialogTitle>
                        <button
                          type="button"
                          className="rounded-full p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700 transition-colors"
                          onClick={closeModal}
                        >
                          <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                        </button>
                      </div>

                      <div className="px-6 py-4 text-left">
                        <p className="text-sm text-gray-600 mb-4 text-left">
                          Pour comprendre l&apos;intégralité des conditions, accédez au <span className="text-[#EA580E] cursor-pointer">Centre d&apos;aide</span>.
                        </p>

                        {/* Policy Options - Dynamically generated */}
                        <motion.div
                          className="space-y-3"
                          variants={modalListParentVariants}
                          initial="hidden"
                          animate="visible"
                        >
                          {policies.map((policy) => (
                            <motion.div
                              key={policy.id}
                              variants={modalListItemVariants}
                              className={`border rounded-md p-4 cursor-pointer transition-colors ${
                                draftPolicyId === policy.id
                                  ? "bg-[#EA580F26] border-[#EA580E] text-[#EA580E]"
                                  : "border-gray-200 hover:bg-gray-50"
                              }`}
                              onClick={() => handlePolicySelect(policy.id)}
                            >
                              <div className="flex justify-between items-center">
                                <h4 className="font-medium">{policy.policy_name}</h4>
                                {draftPolicyId === policy.id && (
                                  <div className="w-6 h-6 flex items-center justify-center bg-[#EA580E] rounded-[2px] text-white">
                                    <CheckIcon className="h-5 w-5" />
                                  </div>
                                )}
                                {draftPolicyId !== policy.id && (
                                  <div className="w-6 h-6 border border-[#EA580E] rounded-[2px]"></div>
                                )}
                              </div>
                              <p className={`text-sm mt-1 leading-relaxed whitespace-pre-line ${draftPolicyId === policy.id ? "text-[#EA580E]" : "text-gray-600"}`}>
                                {policy.policy_descrition}
                              </p>
                            </motion.div>
                          ))}
                        </motion.div>
                      </div>

                      {/* Action Buttons - Sticky Footer */}
                      <div className="sticky bottom-0 bg-white border-t border-gray-200 px-6 py-4 flex justify-between">
                        <button
                          type="button"
                          className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2 rounded-md"
                          onClick={closeModal}
                        >
                          Annuler
                        </button>
                        <button
                          type="button"
                          className="inline-flex justify-center rounded-[2px] border border-transparent bg-[#EA580E] px-4 py-2 text-sm font-medium text-white hover:bg-opacity-90 focus:outline-none focus-visible:ring-2 focus-visible:ring-[#EA580E] focus-visible:ring-offset-2"
                          onClick={savePolicy}
                        >
                          Enregistrer
                        </button>
                      </div>
                    </div>
                  </DialogPanel>
                </div>
              </div>
            </Dialog>
          )}
        </AnimatePresence>

        {/* Add custom styles for toggle switch */}
        <style jsx>{`
          /* The switch - the box around the slider */
          .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
          }

          /* Hide default HTML checkbox */
          .switch input {
            opacity: 0;
            width: 0;
            height: 0;
          }

          /* The slider */
          .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
          }

          .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
          }

          input:checked + .slider {
            background-color: #EA580E;
          }

          input:focus + .slider {
            box-shadow: 0 0 1px #EA580E;
          }

          input:checked + .slider:before {
            transform: translateX(20px);
          }

          /* Rounded sliders */
          .slider.round {
            border-radius: 34px;
          }

          .slider.round:before {
            border-radius: 50%;
          }

          /* Custom scrollbar styles */
          .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
          }

          .scrollbar-thin::-webkit-scrollbar-track {
            background: #f1f1f1;
          }

          .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
          }

          .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: #555;
          }
        `}</style>
      </motion.div>
    </CommonLayout>
  );
};

export default AddConditions;
