"use client"

import { useState, useEffect } from "react"
import { SidebarNav } from "@/components/side-nav"
import { Button } from "@/components/ui/button"
import { <PERSON>u, PanelLeftClose, PanelLeftOpen } from 'lucide-react'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)

  const toggleDesktopSidebar = () => setSidebarOpen(!sidebarOpen)
  const toggleMobileSidebar = () => setMobileSidebarOpen(!mobileSidebarOpen)

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setMobileSidebarOpen(false)
      }
    }
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <div className="flex h-screen bg-[#FCFBFF]">
      {/* Sidebar */}
      <aside
        className={`fixed inset-y-0 left-0 z-50 bg-white transform transition-all duration-300 ease-in-out 
          ${mobileSidebarOpen ? "translate-x-0" : "-translate-x-full"} 
          md:relative md:translate-x-0 ${sidebarOpen ? "md:w-64" : "md:w-0"}`}
      >
        <div className={`w-64 h-full sticky top-0 overflow-y-auto ${!sidebarOpen && "md:hidden"}`}>
          <SidebarNav />
        </div>
      </aside>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="bg-white border-b">
          <div className="flex items-center px-6 py-4 gap-4">
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={toggleMobileSidebar}
            >
              <Menu />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="hidden md:inline-flex"
              onClick={toggleDesktopSidebar}
            >
              {sidebarOpen ? <PanelLeftClose /> : <PanelLeftOpen />}
            </Button>
          </div>
        </header>
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-[#FCFBFF]">
          {children}
        </main>
      </div>

      {/* Overlay to close sidebar on mobile */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={toggleMobileSidebar}
        ></div>
      )}
    </div>
  )
}

