"use client";

import { useState, useEffect } from "react";
import GuestsInput from "../GuestsInput";
import LocationInput from "../LocationInput";
import DatesRangeInput from "../DatesRangeInput";
import { useSearch } from "@/app/(stay-listings)/SearchContext";
import converSelectedDateToString from "@/utils/converSelectedDateToString";
import type { GuestsObject } from "@/app/(client-components)/(HeroSearchFormSmall)/GuestsInput";

interface StaySearchFormProps {
    onSearchChange?: (searchParams: any) => void;
}

const StaySearchForm = ({ onSearchChange }: StaySearchFormProps) => {
    const { searchParams } = useSearch();
    const [fieldNameShow, setFieldNameShow] = useState<"location" | "dates" | "guests">("location");
    const [tempSearchParams, setTempSearchParams] = useState(searchParams);
    const [isCalendarClosing, setIsCalendarClosing] = useState(false);

    // Update parent when temporary search params change
    useEffect(() => {
        if (onSearchChange) {
            onSearchChange(tempSearchParams);
        }
    }, [tempSearchParams, onSearchChange]);

    const handleDatesChange = (startDate: Date | null, endDate: Date | null) => {
        setTempSearchParams((prev) => ({ ...prev, checkIn: startDate, checkOut: endDate }));
    };

    const handleDateSelectionComplete = () => {
        setIsCalendarClosing(true);
        setTimeout(() => {
            setFieldNameShow("guests");
            setIsCalendarClosing(false);
        }, 300); // match CSS transition duration
    };

    // Called on each keystroke in the location input
    const handleLocationChange = (location: string) => {
        setTempSearchParams((prev) => ({ ...prev, location }));
    };

    // Called only when a suggestion is chosen (finalizing the location)
    const handleLocationSelect = (location: string) => {
        setTempSearchParams((prev) => ({ ...prev, location }));
        setFieldNameShow("dates");
    };

    const handleGuestsChange = (guests: GuestsObject) => {
        setTempSearchParams((prev) => ({ ...prev, guests }));
    };

    const renderInputLocation = () => {
        const isActive = fieldNameShow === "location";
        return (
            <div
                className={`w-full bg-white dark:bg-neutral-800 ${
                    isActive ? "rounded-2xl shadow-lg" : "rounded-xl shadow-[0px_2px_2px_0px_rgba(0,0,0,0.25)]"
                } transition-all duration-300 ease-in-out`}
            >
                {!isActive ? (
                    <button
                        className="w-full flex justify-between text-sm font-medium p-4"
                        onClick={() => setFieldNameShow("location")}
                    >
                        <span className="text-neutral-400">Où?</span>
                        <span>{tempSearchParams.location || "Location"}</span>
                    </button>
                ) : (
                    <LocationInput
                        value={tempSearchParams.location || ""}
                        onChange={handleLocationChange}
                        onSelect={handleLocationSelect}
                    />
                )}
            </div>
        );
    };

    const renderInputDates = () => {
        const isActive = fieldNameShow === "dates";
        return (
            <div
                className={`w-full bg-white dark:bg-neutral-800 overflow-hidden ${
                    isActive ? "rounded-2xl shadow-lg" : "rounded-xl shadow-[0px_2px_2px_0px_rgba(0,0,0,0.25)]"
                } transition-all duration-300 ease-in-out`}
            >
                {!isActive ? (
                    <button
                        className="w-full flex justify-between text-sm font-medium p-4"
                        onClick={() => setFieldNameShow("dates")}
                    >
                        <span className="text-neutral-400">Quand</span>
                        <span>
              {tempSearchParams.checkIn
                  ? converSelectedDateToString([tempSearchParams.checkIn, tempSearchParams.checkOut])
                  : "Add date"}
            </span>
                    </button>
                ) : (
                    <DatesRangeInput
                        onDateChange={handleDatesChange}
                        startDate={tempSearchParams.checkIn}
                        endDate={tempSearchParams.checkOut}
                        onDateSelectionComplete={handleDateSelectionComplete}
                        isClosing={isCalendarClosing}
                    />
                )}
            </div>
        );
    };

    const renderInputGuests = () => {
        const isActive = fieldNameShow === "guests";
        let guestSelected = "";
        if (tempSearchParams.guests.guestAdults || tempSearchParams.guests.guestChildren) {
            const guest = (tempSearchParams.guests.guestAdults || 0) + (tempSearchParams.guests.guestChildren || 0);
            guestSelected += `${guest} guests`;
        }
        if (tempSearchParams.guests.guestInfants) {
            guestSelected += `, ${tempSearchParams.guests.guestInfants} infants`;
        }

        return (
            <div
                className={`w-full bg-white dark:bg-neutral-800 overflow-hidden ${
                    isActive ? "rounded-2xl shadow-lg" : "rounded-xl shadow-[0px_2px_2px_0px_rgba(0,0,0,0.25)]"
                } transition-all duration-300 ease-in-out`}
            >
                {!isActive ? (
                    <button
                        className="w-full flex justify-between text-sm font-medium p-4"
                        onClick={() => setFieldNameShow("guests")}
                    >
                        <span className="text-neutral-400">Qui</span>
                        <span>{guestSelected || "Add guests"}</span>
                    </button>
                ) : (
                    <GuestsInput defaultValue={tempSearchParams.guests} onGuestsChange={handleGuestsChange} />
                )}
            </div>
        );
    };

    return (
        <div className="w-full space-y-5">
            {renderInputLocation()}
            {renderInputDates()}
            {renderInputGuests()}
        </div>
    );
};

export default StaySearchForm;
