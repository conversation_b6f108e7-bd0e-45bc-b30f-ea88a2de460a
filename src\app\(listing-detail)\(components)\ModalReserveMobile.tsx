"use client"

import React, { FC, Fragment, useEffect, useState } from "react"
import { Dialog, Transition } from "@headlessui/react"
import { XMarkIcon } from "@heroicons/react/24/solid"
import StayDatesRangeInput from "../listing-stay-detail/StayDatesRangeInput"
import GuestsInput from "../listing-stay-detail/GuestsInput"
import ButtonPrimary from "@/shared/ButtonPrimary"

interface ModalReserveMobileProps {
    renderChildren?: (p: { openModal: () => void }) => React.ReactNode
    listingId: string
    hostId?: string
    pricePerNight: number
    minStay?: number
    maxStay?: number
    maxGuests?: number
    totalPrice: number
    selectedDates: { startDate: Date | null; endDate: Date | null }
    guests: { guestAdults: number; guestChildren: number; guestInfants: number }
    onDatesChange: (dates: { startDate: Date | null; endDate: Date | null; totalPrice: number; numberOfNights: number }) => void
    onGuestsChange: (data: any) => void
    onBookingRequest: () => void
    additionalFees?: { title: string; price: number }[]
    discounts?: { min_period: number; discount_rate: number }[]
    feedbackMessage?: { type: 'error' | 'success' | 'info'; message: string } | null
    isOpen?: boolean
    onOpenChange?: (isOpen: boolean) => void
    propertyPaymentType?: "Cashbased" | "Instant"
}

const ModalReserveMobile: FC<ModalReserveMobileProps> = ({
                                                             renderChildren,
                                                             listingId,
                                                             hostId,
                                                             pricePerNight,
                                                             minStay,
                                                             maxStay,
                                                             maxGuests,
                                                             totalPrice,
                                                             selectedDates,
                                                             guests,
                                                             onDatesChange,
                                                             onGuestsChange,
                                                             onBookingRequest,
                                                             additionalFees = [],
                                                             discounts = [],
                                                             feedbackMessage = null,
                                                             isOpen: controlledIsOpen,
                                                             onOpenChange,
                                                             propertyPaymentType = "Cashbased",
                                                         }) => {
    const [internalIsOpen, setInternalIsOpen] = useState(false)
    const [localDates, setLocalDates] = useState(selectedDates)

    // Determine if we're in controlled or uncontrolled mode
    const isControlled = controlledIsOpen !== undefined && onOpenChange !== undefined
    const isOpen = isControlled ? controlledIsOpen : internalIsOpen

    // Update local dates whenever selected dates change
    useEffect(() => {
        if (selectedDates.startDate || selectedDates.endDate) {
            setLocalDates(selectedDates)
        }
    }, [selectedDates])

    function closeModal() {
        if (isControlled) {
            onOpenChange(false)
        } else {
            setInternalIsOpen(false)
        }
    }

    function openModal() {
        if (isControlled) {
            onOpenChange(true)
        } else {
            setInternalIsOpen(true)
        }
    }

    const handleDatesChange = (dates: { startDate: Date | null; endDate: Date | null; numberOfNights: number; totalPrice: number }) => {
        setLocalDates({ startDate: dates.startDate, endDate: dates.endDate })
        onDatesChange({
            startDate: dates.startDate,
            endDate: dates.endDate,
            totalPrice: dates.totalPrice,
            numberOfNights: dates.numberOfNights
        })
    }

    return (
        <>
            {renderChildren ? renderChildren({ openModal }) : <button onClick={openModal}>Réserver</button>}
            <Transition appear show={isOpen} as={Fragment}>
                <Dialog as="div" className="relative z-50" onClose={closeModal}>
                    <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <div className="fixed inset-0 bg-black bg-opacity-25" />
                    </Transition.Child>

                    <div className="fixed inset-0 overflow-y-auto">
                        <div className="flex items-end justify-center min-h-full p-0 text-center sm:items-center">
                            <Transition.Child
                                as={Fragment}
                                enter="ease-out duration-300"
                                enterFrom="opacity-0 translate-y-full"
                                enterTo="opacity-100 translate-y-0"
                                leave="ease-in duration-200"
                                leaveFrom="opacity-100 translate-y-0"
                                leaveTo="opacity-0 translate-y-full"
                            >
                                <Dialog.Panel className="w-full h-full transform overflow-hidden bg-white text-left align-bottom transition-all sm:align-middle">
                                    <div className="h-screen flex flex-col">
                                        <div className="px-4 py-3 border-b border-gray-200">
                                            <div className="flex items-center justify-between">
                                                <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                                                    Réserver
                                                </Dialog.Title>
                                                <button className="text-gray-400 hover:text-gray-500" onClick={closeModal}>
                                                    <XMarkIcon className="w-6 h-6" />
                                                </button>
                                            </div>
                                        </div>
                                        <div className="flex-grow overflow-y-auto px-4 py-5 sm:p-6">
                                            <div className="flex justify-between items-center mb-6">
                                                <span>Prix par nuit</span>
                                                <span className="text-gray-700">
                                                    à partir de <span className="text-booking-orange font-medium">
                                                        {propertyPaymentType === "Instant" 
                                                            ? Math.ceil(pricePerNight * 1.05) 
                                                            : Math.ceil(pricePerNight)}
                                                    </span> TND
                                                </span>
                                            </div>
                                            <div className="mb-6">
                                                <StayDatesRangeInput
                                                    onDatesChange={handleDatesChange}
                                                    basePrice={pricePerNight}
                                                    listingId={listingId}
                                                    initialDates={localDates}
                                                />
                                            </div>
                                            <div className="mb-6">
                                                <GuestsInput onChange={onGuestsChange} maxGuests={maxGuests || 4} />
                                            </div>

                                            {/* Feedback message display */}
                                            {feedbackMessage && (
                                                <div className={`mb-4 p-3 rounded-lg ${
                                                    feedbackMessage.type === 'error' 
                                                        ? 'bg-red-50 text-red-700 border border-red-200' 
                                                        : feedbackMessage.type === 'success'
                                                            ? 'bg-green-50 text-green-700 border border-green-200'
                                                            : 'bg-blue-50 text-blue-700 border border-blue-200'
                                                }`}>
                                                    <div className="flex items-center">
                                                        {feedbackMessage.type === 'error' && (
                                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                            </svg>
                                                        )}
                                                        {feedbackMessage.type === 'success' && (
                                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                            </svg>
                                                        )}
                                                        {feedbackMessage.type === 'info' && (
                                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clipRule="evenodd" />
                                                            </svg>
                                                        )}
                                                        <span>{feedbackMessage.message}</span>
                                                    </div>
                                                </div>
                                            )}

                                            {localDates.startDate && localDates.endDate && (
                                                <div className="mt-4">
                                                    <div className="flex flex-col space-y-2">
                                                        {/* Base price calculation - with silent 5% increase */}
                                                        <div className="flex justify-between">
                                                            <span>
                                                                {Math.ceil(pricePerNight * 1.05)} Dt x {Math.ceil(
                                                                (localDates.endDate.getTime() - localDates.startDate.getTime()) / (1000 * 60 * 60 * 24)
                                                            )} nuits
                                                            </span>
                                                            <span>{Math.ceil(pricePerNight * 1.05) * Math.ceil(
                                                                (localDates.endDate.getTime() - localDates.startDate.getTime()) / (1000 * 60 * 60 * 24)
                                                            )} Dt</span>
                                                        </div>

                                                        {/* Discount if applicable */}
                                                        {discounts && discounts.length > 0 && Math.ceil(
                                                            (localDates.endDate.getTime() - localDates.startDate.getTime()) / (1000 * 60 * 60 * 24)
                                                        ) >= discounts[0].min_period && (
                                                            <div className="flex justify-between text-green-600">
                                                                <span className="flex items-center">
                                                                    Réduction séjour long ({discounts[0].discount_rate}%)
                                                                </span>
                                                                <span>-{Math.ceil(
                                                                    (discounts[0].discount_rate / 100) * 
                                                                    (Math.ceil(pricePerNight * 1.05) * Math.ceil(
                                                                        (localDates.endDate.getTime() - localDates.startDate.getTime()) / (1000 * 60 * 60 * 24)
                                                                    ))
                                                                )} Dt</span>
                                                            </div>
                                                        )}

                                                        {/* Additional fees */}
                                                        {additionalFees.map((fee, index) => (
                                                            <div key={index} className="flex justify-between">
                                                                <span>{fee.title}</span>
                                                                <span>{fee.price} Dt</span>
                                                            </div>
                                                        ))}
                                                        
                                                        {/* Service Fee (2%) */}
                                                        <div className="flex justify-between">
                                                            <span>Frais de service (2%)</span>
                                                            <span>
                                                                {(() => {
                                                                    const numberOfNights = Math.ceil(
                                                                        (localDates.endDate.getTime() - localDates.startDate.getTime()) / (1000 * 60 * 60 * 24)
                                                                    );
                                                                    const basePrice = pricePerNight * numberOfNights;
                                                                    // Apply 5% increase first (silently)
                                                                    const increasedBasePrice = Math.ceil(basePrice * 1.05);
                                                                    
                                                                    // Calculate discount if applicable
                                                                    let discountAmount = 0;
                                                                    if (discounts && discounts.length > 0 && numberOfNights >= discounts[0].min_period) {
                                                                        discountAmount = Math.ceil((discounts[0].discount_rate / 100) * increasedBasePrice);
                                                                    }
                                                                    
                                                                    // Apply service fee to price after discount
                                                                    const priceAfterDiscount = increasedBasePrice - discountAmount;
                                                                    const serviceFee = Math.ceil(priceAfterDiscount * 0.02);
                                                                    
                                                                    return serviceFee;
                                                                })() } Dt
                                                            </span>
                                                        </div>

                                                        {/* Total */}
                                                        <div className="flex justify-between font-bold pt-2 border-t border-gray-200">
                                                            <span>Total</span>
                                                            <span>{Math.ceil(totalPrice)} Dt</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                            <div className="mt-6">
                                                <ButtonPrimary
                                                    onClick={() => {
                                                        onBookingRequest()
                                                        // Don't close the modal here - let the parent component decide
                                                        // based on the feedback message
                                                    }}
                                                    className="w-full"
                                                >
                                                    Réserver
                                                </ButtonPrimary>
                                            </div>
                                        </div>
                                    </div>
                                </Dialog.Panel>
                            </Transition.Child>
                        </div>
                    </div>
                </Dialog>
            </Transition>
        </>
    )
}

export default ModalReserveMobile
