import type { Metadata } from "next"
import { RecentActivities } from "@/components/recent-activities"
import { getBookings } from "@/app/actions/get-bookings"
import { fetchListingsData } from "@/app/actions/get-listings"
import { BookingsStatus } from "@/components/HostDashboard/bookings/bookings-status"
import { BookingsHistory } from "@/components/HostDashboard/bookings/bookings-history"
import RevenueChart from "./financials/components/RevenueChart/RevenueChart"
import { createClient } from "@/utils/supabase/server"
import { getRecentAuditLogs } from "@/app/actions/get-audit-logs"
export const metadata: Metadata = {
    title: "Tableau de bord",
    description: "Tableau de bord de l'hôte pour gérer les annonces et les réservations",
}

export default async function DashboardPage() {
    const bookings = await getBookings()
    const listingNotificationsData = await fetchListingsData()
    const selectedProperties = listingNotificationsData.map(listing => listing.id)
    console.log(selectedProperties)
    const supabase = await createClient();
  
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    
    // Fetch audit logs for the current user
    const auditLogs = await getRecentAuditLogs(5, user?.id);
    return (
        <>


            {/* Dashboard content - hidden on small screens */}
            <div className="block">
                <div className="p-6 space-y-6">
                    <h2 className="text-3xl font-bold">Bonjour👋,</h2>
                    <div className="hidden lg:block">
                        <BookingsStatus bookings={bookings} />
                    </div>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div className="hidden lg:block lg:col-span-2">
                            <RevenueChart selectedProperties={selectedProperties} selectedPeriod="all" />
                        </div>
                        <RecentActivities auditLogs={auditLogs}/>
                    </div>
                    <div className="hidden lg:block">
                        <BookingsHistory bookings={bookings} />
                    </div>
                </div>
            </div>
        </>
    )
}

