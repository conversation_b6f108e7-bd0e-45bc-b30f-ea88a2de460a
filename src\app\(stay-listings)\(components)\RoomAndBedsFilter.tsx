import { Fragment, useState, useEffect, useCallback } from "react"
import { Popover, Transition } from "@headlessui/react"
import ButtonPrimary from "@/shared/ButtonPrimary"
import ButtonThird from "@/shared/ButtonThird"
import NcInputNumber from "@/components/NcInputNumber"
import type React from "react"
import {useSearch} from "../SearchContext";

interface SelectedFilters {
    propertyTypes: string[]
    propertyCategories: string[]
    amenities: string[]
    beds: number
    bedrooms: number
    bathrooms: number
    kitchens: number
}

interface RoomAndBedsFilterProps {
    beds: number
    bedrooms: number
    bathrooms: number
    kitchens: number
    handleRoomChange: (type: "beds" | "bedrooms" | "bathrooms" | "kitchens", value: number) => void
    clearFilters: (filterType?: "rooms") => void
    applyFilters: (filters: Partial<SelectedFilters>) => void
}

const RoomAndBedsFilter: React.FC<RoomAndBedsFilterProps> = ({
                                                                 beds,
                                                                 bedrooms,
                                                                 bathrooms,
                                                                 kitchens,
                                                                 clearFilters,
                                                                 applyFilters,
                                                             }) => {
    const [tempBeds, setTempBeds] = useState(beds)
    const [tempBedrooms, setTempBedrooms] = useState(bedrooms)
    const [tempBathrooms, setTempBathrooms] = useState(bathrooms)
    const [tempKitchens, setTempKitchens] = useState(kitchens)
    const { searchParams, setSearchParams } = useSearch()

    const renderXClear = useCallback(
        () => (
            <span
                onClick={(e) => {
                    e.stopPropagation() // prevent popover toggle when clicking the X
                    clearFilters("rooms")
                }}
                className="ml-3 flex h-4 w-4 cursor-pointer items-center justify-center rounded-full bg-booking-orange text-white">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
          <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
          />
        </svg>
      </span>
        ),
        [],
    )

    useEffect(() => {
        setTempBeds(beds)
        setTempBedrooms(bedrooms)
        setTempBathrooms(bathrooms)
        setTempKitchens(kitchens)
    }, [beds, bedrooms, bathrooms, kitchens])

    const handleApplyFilters = useCallback(() => {
        applyFilters({
            beds: tempBeds,
            bedrooms: tempBedrooms,
            bathrooms: tempBathrooms,
            kitchens: tempKitchens,
        })
    }, [tempBeds, tempBedrooms, tempBathrooms, tempKitchens, applyFilters])

    const handleClearFilters = useCallback(() => {
        setTempBeds(0)
        setTempBedrooms(0)
        setTempBathrooms(0)
        setTempKitchens(0)
        clearFilters("rooms")
    }, [clearFilters])

    const isAnyFilterSelected = searchParams.beds + searchParams.bathrooms + searchParams.bedrooms + searchParams.kitchens > 0
    const numFiltersSelected = (searchParams.beds !== 0 ? 1 : 0) + (searchParams.bathrooms !== 0 ? 1 : 0) + (searchParams.bedrooms !== 0 ? 1 : 0) + (searchParams.kitchens!== 0 ? 1 : 0);

    return (
        <Popover className="relative">
            {({ open, close }) => (
                <>
                    <Popover.Button
                        className={`flex items-center justify-center rounded-full border border-neutral-300 px-3 py-2 text-sm hover:border-neutral-400 focus:outline-none dark:border-neutral-700 dark:hover:border-neutral-6000
                          ${open ? "!border-booking-orange" : ""}
                          ${isAnyFilterSelected ? "bg-light-orange text-booking-orange !border-booking-orange" : ""}
                        `}
                    >
                        <span>Chambres et lits {isAnyFilterSelected ? `(${numFiltersSelected})` : ""}</span>
                        {isAnyFilterSelected ? renderXClear() : <i className="las la-angle-down ml-2"></i>}
                    </Popover.Button>
                    <Transition
                        as={Fragment}
                        enter="transition ease-out duration-200"
                        enterFrom="opacity-0 translate-y-1"
                        enterTo="opacity-100 translate-y-0"
                        leave="transition ease-in duration-150"
                        leaveFrom="opacity-100 translate-y-0"
                        leaveTo="opacity-0 translate-y-1"
                    >
                        <Popover.Panel className="absolute left-0 z-10 mt-3 w-screen max-w-sm px-4 sm:px-0 lg:max-w-md">
                            <div className="overflow-hidden rounded-2xl border border-neutral-200 bg-white shadow-xl dark:border-neutral-700 dark:bg-neutral-900">
                                <div className="relative flex flex-col space-y-5 px-5 py-6">
                                    <NcInputNumber label="Lits" max={10} value={tempBeds} onChange={setTempBeds} />
                                    <NcInputNumber label="Chambres" max={10} value={tempBedrooms} onChange={setTempBedrooms} />
                                    <NcInputNumber label="Salles de bain" max={10} value={tempBathrooms} onChange={setTempBathrooms} />
                                    <NcInputNumber label="Cuisines" max={10} value={tempKitchens} onChange={setTempKitchens} />
                                </div>
                                <div className="flex items-center justify-between bg-neutral-50 p-5 dark:border-t dark:border-neutral-800 dark:bg-neutral-900">
                                    <ButtonThird
                                        onClick={() => {
                                            handleClearFilters()
                                            close()
                                        }}
                                        sizeClass="px-4 py-2 sm:px-5"
                                    >
                                        Effacer
                                    </ButtonThird>
                                    <ButtonPrimary
                                        onClick={() => {
                                            handleApplyFilters()
                                            close()
                                        }}
                                        sizeClass="px-4 py-2 sm:px-5"
                                    >
                                        Appliquer
                                    </ButtonPrimary>
                                </div>
                            </div>
                        </Popover.Panel>
                    </Transition>
                </>
            )}
        </Popover>
    )
}

export default RoomAndBedsFilter

