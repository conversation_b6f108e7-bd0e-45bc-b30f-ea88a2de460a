"use client"

import type React from "react"
import { useCallback, useState, useRef, useEffect } from "react"
import Pagination from "@/shared/Pagination"
import TabFilters from "./TabFilters"
import ListingMapContainer from "@/components/ListingMapContainer"
import type { ListingData } from "@/data/types"
import NewStayCard from "@/components/NewStayCard"
import { mapListingToStayData } from "@/utils/mapListingToStayData"
import { useSearch, type SearchParams } from "./SearchContext"
import StayCardH from "@/components/StayCardH"
import MobileSearchHeader from "@/app/(client-components)/(HeroSearchForm2Mobile)/MobileSearchHeader"
import Image from "next/image"

export type SectionGridHasMapProps = {}

const SectionGridHasMap: React.FC<SectionGridHasMapProps> = () => {
	const { searchParams, setSearchParams, mobileTabFiltersVisible, setMobileTabFiltersVisible } = useSearch()
	const [currentHoverID, setCurrentHoverID] = useState<string | number>(-1)
	const [showFullMapFixed, setShowFullMapFixed] = useState(true)
	const [showMap, setShowMap] = useState(true)
	const [listings, setListings] = useState<ListingData[]>([])
	const [mapCenter, setMapCenter] = useState({ lat: 33.9466528617825, lng: 9.36381913403275 })
	const [mapZoom, setMapZoom] = useState(6.7)
	const [isLoading, setIsLoading] = useState(false)
	const [currentPage, setCurrentPage] = useState(1)
	const [totalPages, setTotalPages] = useState(1)
	const [totalCount, setTotalCount] = useState(0)
	const pageSize = 20
	const mapBoundsRef = useRef<google.maps.LatLngBounds | null>(null)
	const mapRef = useRef<google.maps.Map | null>(null)
	const abortControllerRef = useRef<AbortController | null>(null)
	const [selectedListing, setSelectedListing] = useState<ListingData | null>(null)
	// Add a state to track the source of the hover
	const [hoverSource, setHoverSource] = useState<"map" | "grid">("map")
	// Add a timeout ref for hover debouncing
	const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null)
	// NEW: Add scroll detection
	const [isScrollingDown, setIsScrollingDown] = useState(false)
	const [headerTransformed, setHeaderTransformed] = useState(false)
	const lastScrollY = useRef(0)
	const scrollThreshold = 50 // Minimum scroll amount to trigger animations

	// Add Filters and FilterOption types
	interface FilterOption {
		icons: string | undefined;
		id: string;
		name: string;
	}
	interface Filters {
		propertyTypes: FilterOption[];
		propertyCategories: FilterOption[];
		amenities: FilterOption[];
		roomTypes: FilterOption[];
	}
	const [filters, setFilters] = useState<Filters>({
		propertyTypes: [],
		propertyCategories: [],
		amenities: [],
		roomTypes: [],
	});
	const [isLoadingCategories, setIsLoadingCategories] = useState(true);

	// Clear hover timeout
	const clearHoverTimeout = useCallback(() => {
		if (hoverTimeoutRef.current) {
			clearTimeout(hoverTimeoutRef.current)
			hoverTimeoutRef.current = null
		}
	}, [])

	// Handle marker hover with debounce
	const handleMarkerHover = useCallback(
		(listing: ListingData | null) => {
			clearHoverTimeout()

			hoverTimeoutRef.current = setTimeout(() => {
				if (listing) {
					// Only update if it's different from current hover ID
					if (currentHoverID !== listing.propertyId) {
						setCurrentHoverID(listing.propertyId || -1)
						setHoverSource("map")
					}
				} else {
					// Only reset if we're currently hovering something
					if (currentHoverID !== -1) {
						setCurrentHoverID(-1)
					}
				}
			}, 50) // Short debounce to prevent flickering
		},
		[currentHoverID, clearHoverTimeout],
	)

	// Function to create query params from search params and map bounds
	const createQueryParams = useCallback(() => {
		const queryParams = new URLSearchParams({
			page: currentPage.toString(),
			pageSize: pageSize.toString(),
		})

		// Add map bounds if available
		if (mapBoundsRef.current) {
			const ne = mapBoundsRef.current.getNorthEast()
			const sw = mapBoundsRef.current.getSouthWest()
			queryParams.set("neLat", ne.lat().toString())
			queryParams.set("neLng", ne.lng().toString())
			queryParams.set("swLat", sw.lat().toString())
			queryParams.set("swLng", sw.lng().toString())
		}

		// Add search params
		if (searchParams.beds) queryParams.set("beds", searchParams.beds.toString())
		if (searchParams.bedrooms) queryParams.set("bedrooms", searchParams.bedrooms.toString())
		if (searchParams.bathrooms) queryParams.set("bathrooms", searchParams.bathrooms.toString())
		if (searchParams.kitchens) queryParams.set("kitchens", searchParams.kitchens.toString())
		if (searchParams.checkIn) queryParams.set("checkIn", searchParams.checkIn.toISOString())
		if (searchParams.checkOut) queryParams.set("checkOut", searchParams.checkOut.toISOString())
		if (searchParams.guests.guestAdults) queryParams.set("guestAdults", searchParams.guests.guestAdults.toString())
		if (searchParams.guests.guestChildren)
			queryParams.set("guestChildren", searchParams.guests.guestChildren.toString())
		if (searchParams.guests.guestInfants) queryParams.set("guestInfants", searchParams.guests.guestInfants.toString())
		if (searchParams.priceRange?.[0]) queryParams.set("minPrice", searchParams.priceRange[0].toString())
		if (searchParams.priceRange?.[1]) queryParams.set("maxPrice", searchParams.priceRange[1].toString())
		if (searchParams.paymentType) queryParams.set("paymentType", searchParams.paymentType)
		if (searchParams.location) queryParams.set("location", searchParams.location)

		// Add array params
		if (searchParams.propertyTypes.length > 0) {
			searchParams.propertyTypes.forEach((type) => queryParams.append("propertyTypes[]", type))
		}
		if (searchParams.propertyCategories.length > 0) {
			searchParams.propertyCategories.forEach((category) => queryParams.append("propertyCategories[]", category))
		}
		if (searchParams.amenities.length > 0) {
			searchParams.amenities.forEach((amenity) => queryParams.append("amenities[]", amenity))
		}
		// Add room types to query params
		if (searchParams.roomTypes && searchParams.roomTypes.length > 0) {
			searchParams.roomTypes.forEach((roomType) => queryParams.append("roomTypes[]", roomType))
		}
		if (searchParams.minRating) queryParams.set("minRating", searchParams.minRating.toString())

		return queryParams
	}, [currentPage, pageSize, searchParams])

	// Fetch listings with the current search params
	const fetchListings = useCallback(async () => {
		// Cancel any in-progress fetch
		if (abortControllerRef.current) {
			abortControllerRef.current.abort()
		}

		// Create a new abort controller for this fetch
		abortControllerRef.current = new AbortController()
		const signal = abortControllerRef.current.signal

		setIsLoading(true)
		setListings([]) // Clear existing listings to prevent duplicates

		try {
			const queryParams = createQueryParams()
			const response = await fetch(`/api/listings?${queryParams}`, { signal })

			if (!response.ok) {
				throw new Error("Failed to fetch listings")
			}

			const data = await response.json()
			setListings(data.listings)
			setTotalCount(data.totalCount)
			setTotalPages(data.totalPages)
		} catch (error) {
			// Only log errors that aren't from aborting
			if (!(error instanceof DOMException && error.name === "AbortError")) {
				console.error("Error fetching listings:", error)
			}
		} finally {
			setIsLoading(false)
		}
	}, [createQueryParams])

	// Effect to fetch listings when search params or page changes
	useEffect(() => {
		fetchListings()

		// Cleanup function to abort any in-progress fetch when component unmounts
		// or when dependencies change
		return () => {
			if (abortControllerRef.current) {
				abortControllerRef.current.abort()
			}
		}
	}, [fetchListings])

	useEffect(() => {
		if (!showFullMapFixed) {
			setSelectedListing(null)
		}
	}, [showFullMapFixed])

	// Handle location search
	useEffect(() => {
		if (searchParams.location) {
			if (typeof window !== "undefined" && typeof window.google !== "undefined") {
				const geocoder = new window.google.maps.Geocoder()
				geocoder.geocode({ address: searchParams.location }, (results, status) => {
					if (status === window.google.maps.GeocoderStatus.OK && results && results[0]) {
						const { lat, lng } = results[0].geometry.location
						setMapCenter({ lat: lat(), lng: lng() })
						setMapZoom(12)
						if (mapRef.current) {
							mapRef.current.panTo({ lat: lat(), lng: lng() })
						}
					}
				})
			}
		}
	}, [searchParams.location])

	// Handle map bounds change
	const handleBoundsChanged = useCallback(
		(newBounds: google.maps.LatLngBounds) => {
			mapBoundsRef.current = newBounds
			fetchListings()
		},
		[fetchListings],
	)

	// Handle filter changes
	const handleFiltersChange = useCallback(
		(newFilters: SearchParams) => {
			setCurrentPage(1) // Reset to first page when filters change
			setSearchParams(newFilters)
			// fetchListings will be called automatically by the effect
		},
		[setSearchParams],
	)

	// Handle page change
	const handlePageChange = useCallback((newPage: number) => {
		setCurrentPage(newPage)
		// fetchListings will be called automatically by the effect
	}, [])

	// Cleanup timeouts on unmount
	useEffect(() => {
		return () => {
			clearHoverTimeout()
		}
	}, [clearHoverTimeout])

	// Fetch filters (categories) on mount
	useEffect(() => {
		const fetchFilters = async () => {
			setIsLoadingCategories(true);
			try {
				const response = await fetch("/api/filters");
				if (response.ok) {
					const data = await response.json();
					setFilters(data);
				} else {
					console.error("Failed to fetch filters");
				}
			} catch (error) {
				console.error("Error fetching filters:", error);
			} finally {
				setIsLoadingCategories(false);
			}
		};
		fetchFilters();
	}, []);

	// Handler for category selection
	const handleCategoryChange = useCallback(
		(categoryId: string) => {
			setSearchParams((prevParams) => {
				const newCategories = prevParams.propertyCategories.includes(categoryId)
					? prevParams.propertyCategories.filter((id) => id !== categoryId)
					: [...prevParams.propertyCategories, categoryId];
				return { ...prevParams, propertyCategories: newCategories };
			});
		},
		[setSearchParams],
	);

	// Add a useEffect for scroll detection after the existing useEffects (around line ~211)
	// Add this before the return statement
	// Implement scroll detection for header animations
	useEffect(() => {
		const handleScroll = () => {
			const currentScrollY = window.scrollY
			if (currentScrollY < 10) {
				// At the top of the page - reset any scroll states
				setIsScrollingDown(false)
				lastScrollY.current = currentScrollY
				return
			}
			
			// Determine if scrolling up or down by comparing with last position
			if (currentScrollY > lastScrollY.current + scrollThreshold) {
				setIsScrollingDown(true)
				lastScrollY.current = currentScrollY
			} else if (currentScrollY < lastScrollY.current - scrollThreshold) {
				setIsScrollingDown(false) 
				lastScrollY.current = currentScrollY
			}
		}
		
		// Only add the listener if we're on the client
		if (typeof window !== "undefined") {
			window.addEventListener("scroll", handleScroll, { passive: true })
		}
		
		return () => {
			if (typeof window !== "undefined") {
				window.removeEventListener("scroll", handleScroll)
			}
		}
	}, [scrollThreshold])

	return (
		<div>
			<MobileSearchHeader
				isScrollingDown={isScrollingDown}
				setHeaderTransformed={setHeaderTransformed}
				headerTransformed={headerTransformed}
			/>

			<TabFilters
				className="hidden md:block mb-5"
				onFiltersChange={handleFiltersChange}
				desktopMap={showMap}
				onToggleDesktopMap={(value: boolean) => setShowMap(value)}
				mobileMap={showFullMapFixed}
				onToggleMobileMap={(value: boolean) => setShowFullMapFixed(value)}
			/>

			{/* Mobile TabFilters with animation */}
			<div
				className={`md:hidden transition-all duration-300 ease-in-out ${
					mobileTabFiltersVisible ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0 overflow-hidden"
				}`}
			>
				<TabFilters
					onFiltersChange={handleFiltersChange}
					desktopMap={showMap}
					onToggleDesktopMap={(value: boolean) => setShowMap(value)}
					mobileMap={showFullMapFixed}
					onToggleMobileMap={(value: boolean) => setShowFullMapFixed(value)}
				/>
			</div>

			{/* Main grid layout: listings (categories+grid) | map */}
			<div className={`relative grid min-h-screen w-full ${showMap ? 'grid-cols-1 lg:grid-cols-2' : 'grid-cols-1' } gap-0`}>
				{/* Left: Categories bar + Listings grid */}
				<div
					className={`flex flex-col min-h-screen w-full pt-4 lg:pt-0 ${
						showMap ? 'lg:w-full max-w-[1184px] xl:pr-8 xl:pl-10 2xl:pl-20' : 'w-full max-w-none 2xl:px-0'
					} flex-shrink-0`}
				>
					{/* Property Categories Bar */}
					<div className="relative mt-4 sm:mx-4 xl:mx-10 2xl:mx-20">
						{/* Left scroll arrow */}
						<button
							className="absolute left-0 top-[40%] -translate-y-1/2 z-9 bg-white rounded-full shadow-md p-2 hover:bg-gray-50 hidden sm:block"
							onClick={() => {
								const container = document.getElementById("category-scroll-container");
								if (container) {
									container.scrollBy({ left: -110, behavior: "smooth" });
								}
							}}
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								className="h-5 w-5 text-gray-600"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
							</svg>
						</button>
						{/* Categories container */}
						<div
							id="category-scroll-container"
							className="flex gap-10 overflow-x-auto py-3 px-4 sm:mx-11 sm:px-0 scrollbar-hide border-y border-neutral-200 sm:border-0"
						>
							{isLoadingCategories
								? Array(16)
										.fill(0)
										.map((_, index) => (
											<div key={index} className="flex flex-col items-center">
												<div className="w-14 h-6 bg-gray-200 rounded-md animate-pulse mb-1"></div>
												<div className="w-16 h-4 bg-gray-200 rounded-md animate-pulse"></div>
											</div>
										))
								: filters.propertyCategories.map((category) => (
										<button
											key={category.id}
											onClick={() => handleCategoryChange(category.id)}
											className={`group flex flex-col items-center hover:text-black ${
												searchParams.propertyCategories.includes(category.id)
													? "text-black"
													: "text-neutral-500"
											}`}
										>
											<div className="w-14 h-6 flex items-end justify-center mb-1">
												<Image
													src={category.icons || "/placeholder.svg"}
													alt={category.name}
													height={24}
													width={24}
													className={`$ {
														searchParams.propertyCategories.includes(category.id)
															? "opacity-100"
															: "opacity-70"
													} group-hover:opacity-100`}
												/>
											</div>
											<span className="text-xs font-medium group-active:scale-90">{category.name}</span>
										</button>
									))}
						</div>
						{/* Right scroll arrow */}
						<button
							className="absolute right-0 top-[40%] -translate-y-1/2 z-9 bg-white rounded-full shadow-md p-2 hover:bg-gray-50 hidden sm:block"
							onClick={() => {
								const container = document.getElementById("category-scroll-container");
								if (container) {
									container.scrollBy({ left: 100, behavior: "smooth" });
								}
							}}
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								className="h-5 w-5 text-gray-600"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
							</svg>
						</button>
					</div>
					{/* Listings grid */}
					<div
						className={`grid grid-cols-1 gap-x-5 gap-y-8 sm:grid-cols-2 ${
							showMap ? "lg:grid-cols-2 2xl:gap-x-5 px-6 pt-2" : "lg:grid-cols-4"
						}`}
					>
						{isLoading ? (
							<div className="flex justify-center items-center h-64">
								<span className="loading loading-spinner loading-lg"></span>
							</div>
						) : (
							listings.map((item) => (
								<div
									key={item.propertyId}
									onMouseEnter={() => {
										clearHoverTimeout()
										hoverTimeoutRef.current = setTimeout(() => {
											setCurrentHoverID(item.propertyId || -1)
											setHoverSource("grid")
										}, 50)
									}}
									onMouseLeave={() => {
										clearHoverTimeout()
										hoverTimeoutRef.current = setTimeout(() => {
											setCurrentHoverID(-1)
										}, 100)
									}}
								>
									<NewStayCard data={mapListingToStayData(item)} />
								</div>
							))
						)}
					</div>
					{totalPages > 1 && (
						<div className="mt-16 flex items-center justify-center">
							<Pagination currentPage={currentPage} totalPages={totalPages} onPageChange={handlePageChange} />
						</div>
					)}
				</div>
				{/* Right: Map */}
				{showMap && (
					<div className=" lg:block lg:flex-1 hidden lg:sticky lg:top-[88px] lg:h-[calc(100vh-88px)]">
						<div className="fixed left-0 top-0 h-full w-full overflow-hidden rounded-md lg:sticky lg:top-[88px] lg:h-[calc(100vh-88px)]">
							<ListingMapContainer
								currentHoverID={currentHoverID}
								DEMO_DATA={listings}
								mapCenter={mapCenter}
								mapZoom={mapZoom}
								onBoundsChanged={handleBoundsChanged}
								onCenterChanged={(center) => setMapCenter(center)}
								onZoomChanged={(zoom) => setMapZoom(zoom)}
								onMarkerHover={handleMarkerHover}
								mapRef={mapRef}
								hoverSource={hoverSource}
							/>
						</div>
					</div>
				)}
				{/* Mobile Map Container */}
				{showFullMapFixed && (
					<div className="fixed inset-0 z-40 lg:hidden">
						<div className="fixed left-0 top-0 h-full w-full overflow-hidden rounded-md">
							<ListingMapContainer
								currentHoverID={currentHoverID}
								DEMO_DATA={listings}
								mapCenter={mapCenter}
								mapZoom={mapZoom}
								onBoundsChanged={handleBoundsChanged}
								onCenterChanged={(center) => setMapCenter(center)}
								onZoomChanged={(zoom) => setMapZoom(zoom)}
								onMarkerClick={(listing) => setSelectedListing(listing)}
								mapRef={mapRef}
								hoverSource={hoverSource}
							/>
						</div>
					</div>
				)}
				{selectedListing && (
					<div className="fixed bottom-0 left-0 right-0 z-50 p-4 shadow-lg lg:hidden">
						{/* You can use the NewStayCard or your custom card component */}
						<StayCardH data={mapListingToStayData(selectedListing)} />
					</div>
				)}
				
				{/* Floating Map Toggle Button - Mobile Only */}
				<div className={`fixed ${selectedListing ? 'bottom-32' : 'bottom-16'} left-1/2 transform -translate-x-1/2 z-50 lg:hidden`}>
					<button
						onClick={() => setShowFullMapFixed(!showFullMapFixed)}
						className={`flex items-center justify-center rounded-full py-3 px-6 gap-2 transition-all duration-300 shadow-lg "bg-gray-700 dark:bg-white/80 backdrop-blur-xl backdrop-filter hover:bg-gray-800 dark:hover:bg-white/90 text-white dark:text-gray-700 border border-neutral-700/80 dark:border-neutral-200/80`}
					>
						<div className="relative">
							<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg" className="relative z-10">
								<path d="M6.125 4.34409V10.8753M10.875 6.12534V12.6566M11.2733 15.4262L15.1327 13.4965C15.4344 13.3456 15.625 13.0373 15.625 12.6999V2.81641C15.625 2.15433 14.9283 1.72372 14.3361 2.01981L11.2733 3.55119C11.0226 3.67656 10.7274 3.67656 10.4767 3.55119L6.5233 1.57449C6.27256 1.44913 5.97744 1.44913 5.7267 1.57449L1.86733 3.50418C1.5656 3.65505 1.375 3.96344 1.375 4.30078V14.1843C1.375 14.8464 2.07175 15.277 2.66392 14.9809L5.7267 13.4495C5.97744 13.3241 6.27256 13.3241 6.5233 13.4495L10.4767 15.4262C10.7274 15.5516 11.0226 15.5516 11.2733 15.4262Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
							</svg>
							{!showFullMapFixed && (
								<div className="absolute -z-10 inset-0 opacity-30">
									<span className="absolute -left-1 -top-1 bg-booking-orange w-4 h-4 rounded-full mix-blend-multiply filter blur-md"></span>
									<span className="absolute -right-1 -bottom-1 bg-dark-blue w-4 h-4 rounded-full mix-blend-multiply filter blur-md"></span>
								</div>
							)}
						</div>
						<span className="font-medium text-sm">{showFullMapFixed ? "Liste" : "Carte"}</span>
					</button>
				</div>
			</div>
		</div>
	)
}

export default SectionGridHasMap
