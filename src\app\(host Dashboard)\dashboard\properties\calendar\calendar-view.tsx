"use client"

import { useState, use<PERSON>emo, use<PERSON>onte<PERSON>t, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Filter, ChevronLeft, ChevronRight, X, Lock, CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { Card } from "@/components/ui/card"
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet"
import type { Booking } from "@/app/actions/get-bookings"
import { getBookingById } from "@/app/actions/get-booking-by-id"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { CalendarProvider, CalendarContext, type BlockedDate } from "./calendar-context"
import { UserProvider } from "@/contexts/UserContext"
import { format } from "date-fns"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { toast } from "react-hot-toast"

const MONTHS = [
  "Jan<PERSON>",
  "<PERSON>é<PERSON><PERSON>",
  "<PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>û<PERSON>",
  "Septembre",
  "Octobre",
  "Novembre",
  "Décembre",
]

const DAYS = ["DIM", "LUN", "MAR", "MER", "JEU", "VEN", "SAM"]

interface CalendarViewProps {
  initialBookings: Booking[]
}

export function CalendarView({ initialBookings }: CalendarViewProps) {
  return (
    <UserProvider>
      <CalendarProvider>
        <CalendarViewContent initialBookings={initialBookings} />
      </CalendarProvider>
    </UserProvider>
  )
}

function CalendarViewContent({ initialBookings }: { initialBookings: Booking[] }) {
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear())
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth())
  const [view, setView] = useState<"mensuel" | "annuel">("mensuel")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null)
  const [isBookingSheetOpen, setIsBookingSheetOpen] = useState(false)
  const [selectedBlockedDates, setSelectedBlockedDates] = useState<BlockedDate[]>([])
  const [isBlockedDateSheetOpen, setIsBlockedDateSheetOpen] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  // State for ICS copy feedback
  const [icsCopied, setIcsCopied] = useState(false)
  // State for block/unblock dialog
  const [blockDialog, setBlockDialog] = useState<{ open: boolean, date: Date | null, action: 'block' | 'unblock', blockId?: string }>({ open: false, date: null, action: 'block' })
  const [blockLoading, setBlockLoading] = useState(false)

  // Get the context values (declare only once)
  const { hostId, setHostId, blockedDates, isLoading, error, fetchBlockedDates } = useContext(CalendarContext)

  // Get all unique listings from bookings
  const listings = useMemo(() => {
    const map = new Map<string, { id: string; title: string }>()
    initialBookings.forEach((booking) => {
      if (booking.listing && booking.listing.id && booking.listing.title) {
        map.set(booking.listing.id, { id: booking.listing.id, title: booking.listing.title })
      }
    })
    // Optionally, add listings from blockedDates if not present in bookings
    blockedDates.forEach((bd) => {
      if (bd.listing_id && bd.listing_title && !map.has(bd.listing_id)) {
        map.set(bd.listing_id, { id: bd.listing_id, title: bd.listing_title })
      }
    })
    return Array.from(map.values())
  }, [initialBookings, blockedDates])

  // State for selected listing
  const [selectedListingId, setSelectedListingId] = useState<string | null>(null)

  // Set default selected listing on first load or when listings change
  useEffect(() => {
    if (listings.length > 0 && (!selectedListingId || !listings.find(l => l.id === selectedListingId))) {
      setSelectedListingId(listings[0].id)
    }
  }, [listings, selectedListingId])

  // Filter bookings and blocked dates by selected listing
  const filteredBookings = useMemo(() => {
    if (!selectedListingId) return []
    return initialBookings.filter(
      (booking) =>
        (booking.status === "confirmed" || booking.status === "pending") &&
        booking.listing.id === selectedListingId &&
        (booking.listing.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          booking.listing.address.toLowerCase().includes(searchQuery.toLowerCase())),
    )
  }, [initialBookings, searchQuery, selectedListingId])

  const filteredBlockedDates = useMemo(() => {
    if (!selectedListingId) return []
    return blockedDates.filter((bd) => bd.listing_id === selectedListingId)
  }, [blockedDates, selectedListingId])

  const handlePreviousMonth = () => {
    if (currentMonth === 0) {
      setCurrentMonth(11)
      setCurrentYear(currentYear - 1)
    } else {
      setCurrentMonth(currentMonth - 1)
    }
  }

  const handleNextMonth = () => {
    if (currentMonth === 11) {
      setCurrentMonth(0)
      setCurrentYear(currentYear + 1)
    } else {
      setCurrentMonth(currentMonth + 1)
    }
  }

  const handleBookingClick = async (bookingId: string) => {
    const booking = await getBookingById(bookingId)
    setSelectedBooking(booking)
    setIsBookingSheetOpen(true)
  }

  const handleBlockedDateClick = (date: Date) => {
    // Find all blocked dates for this day
    const dayBlockedDates = blockedDates.filter((bd) => {
      const blockStart = new Date(bd.start_date)
      const blockEnd = new Date(bd.end_date)
      blockStart.setHours(0, 0, 0, 0)
      blockEnd.setHours(0, 0, 0, 0)
      const currentDate = new Date(date)
      currentDate.setHours(0, 0, 0, 0)
      return currentDate >= blockStart && currentDate <= blockEnd
    })

    setSelectedBlockedDates(dayBlockedDates)
    setSelectedDate(date)
    setIsBlockedDateSheetOpen(true)
  }

  // Add handler for single day block
  const handleDayClick = (date: Date, isBlocked: boolean, blockId?: string) => {
    if (!selectedListingId) return
    if (isBlocked && blockId) {
      setBlockDialog({ open: true, date, action: 'unblock', blockId })
    } else if (!isBlocked) {
      setBlockDialog({ open: true, date, action: 'block' })
    }
  }

  // Helper to format date as YYYY-MM-DD in local time
  function formatLocalDate(date: Date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {/* Listing Filter Dropdown */}
      <div className="flex items-center gap-4 mb-2">
        <label htmlFor="listing-select" className="font-medium text-sm">Séjour:</label>
        <select
          id="listing-select"
          className="border rounded px-2 py-1 text-sm"
          value={selectedListingId || ''}
          onChange={e => setSelectedListingId(e.target.value)}
        >
          {listings.map(listing => (
            <option key={listing.id} value={listing.id}>{listing.title}</option>
          ))}
        </select>
        {/* Copier le lien ICS */}
        {selectedListingId && (
          <button
            type="button"
            onClick={async () => {
              await navigator.clipboard.writeText(`${window.location.origin}/api/ical/${selectedListingId}`)
              setIcsCopied(true)
              setTimeout(() => setIcsCopied(false), 2000)
            }}
            className="ml-2 px-3 py-1 rounded bg-[#365df5] text-white text-xs font-medium hover:bg-[#274bb5] transition-colors"
            title="Copier le lien du flux calendrier"
          >
            {icsCopied ? 'Lien copié !' : 'Copier le lien ICS'}
          </button>
        )}
      </div>

      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center gap-2 overflow-x-auto pb-2 sm:pb-0">
            <Button variant="outline" size="sm">
              Aujourd&apos;hui
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={view === "annuel" ? () => setCurrentYear(currentYear - 1) : handlePreviousMonth}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-lg font-medium px-2 whitespace-nowrap">
              {view === "annuel" ? currentYear : `${MONTHS[currentMonth]} ${currentYear}`}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={view === "annuel" ? () => setCurrentYear(currentYear + 1) : handleNextMonth}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center gap-2 overflow-x-auto pb-2 sm:pb-0">
            <Button
              variant={view === "mensuel" ? "default" : "ghost"}
              onClick={() => setView("mensuel")}
              className="text-sm whitespace-nowrap"
              size="sm"
            >
              Mensuel
            </Button>
            <Button
              variant={view === "annuel" ? "default" : "ghost"}
              onClick={() => setView("annuel")}
              className="text-sm whitespace-nowrap"
              size="sm"
            >
              Annuel
            </Button>
          </div>
        </div>

        <div className="p-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#fa644c]"></div>
            </div>
          ) : (!selectedListingId || (filteredBookings.length === 0 && filteredBlockedDates.length === 0)) ? (
            <div className="text-center text-muted-foreground py-12">No bookings or blocked dates for this listing.</div>
          ) : view === "annuel" ? (
            <AnnualView
              year={currentYear}
              month={currentMonth}
              bookings={filteredBookings}
              onBookingClick={handleBookingClick}
              onDayClick={handleDayClick}
              blockedDates={filteredBlockedDates}
            />
          ) : (
            <MonthView
              year={currentYear}
              month={currentMonth}
              bookings={filteredBookings}
              onBookingClick={handleBookingClick}
              onDayClick={handleDayClick}
              blockedDates={filteredBlockedDates}
            />
          )}
        </div>

        <div className="p-4 border-t flex flex-wrap gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-[#365df5]" />
            <span className="text-sm">Réservation confirmée</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-[#fa644c]" />
            <span className="text-sm">Réservation en attente</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-[#fa644c]/70" />
            <span className="text-sm">Date bloquée</span>
          </div>
        </div>
      </div>

      {/* Booking Sheet */}
      <Sheet open={isBookingSheetOpen} onOpenChange={setIsBookingSheetOpen}>
        <SheetContent className="w-[400px] sm:w-[540px] p-0">
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-4 rounded-full"
            onClick={() => setIsBookingSheetOpen(false)}
          >
            <X className="h-4 w-4" />
          </Button>
          <div className="flex flex-col h-full">
            {selectedBooking && (
              <>
                <div className="relative h-[40vh] w-full">
                  <Image
                    src={selectedBooking.listing.featured_image_url || "/placeholder.svg"}
                    alt={selectedBooking.listing.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute bottom-4 left-4 right-4 text-white">
                    <h3 className="font-semibold text-2xl mb-1">{selectedBooking.listing.title}</h3>
                    <p className="text-sm opacity-90">{selectedBooking.listing.address}</p>
                  </div>
                </div>

                <div className="flex-1 overflow-auto">
                  <div className="p-6 space-y-6">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="font-semibold text-lg">{"Guest"}</h3>
                        <p className="text-sm text-muted-foreground">Order ID: {selectedBooking.id.slice(0, 6)}</p>
                      </div>
                      <Badge
                        variant="outline"
                        className={
                          selectedBooking.status === "confirmed"
                            ? "bg-green-100 text-green-800 border-green-300"
                            : selectedBooking.status === "pending"
                              ? "bg-yellow-100 text-yellow-800 border-yellow-300"
                              : "bg-red-100 text-red-800 border-red-300"
                        }
                      >
                        {selectedBooking.status.charAt(0).toUpperCase() + selectedBooking.status.slice(1)}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 gap-6">
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Arrivée</p>
                        <p className="font-medium">
                          {new Date(selectedBooking.start_date).toLocaleDateString("en-US", {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          })}
                        </p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Départ</p>
                        <p className="font-medium">
                          {new Date(selectedBooking.end_date).toLocaleDateString("en-US", {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          })}
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-6">
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Invités</p>
                        <p className="font-medium">{selectedBooking.num_guests || 1} Adult</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Type de listing</p>
                        <p className="font-medium">{"villa"}</p>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-4">Détails du prix</h4>
                      <div className="space-y-2">
                        {(() => {
                          const checkIn = new Date(selectedBooking.start_date)
                          const checkOut = new Date(selectedBooking.end_date)
                          const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
                          const nightlyRate = selectedBooking.listing.listing_pricing?.nightly_rate || 0
                          const basePrice = nightlyRate * nights
                          const serviceFee = basePrice * 0.1 // 10% service fee
                          const vatFee = basePrice * 0.05 // 5% VAT
                          const securityFee = basePrice * 0.05 // 5% security fee
                          const total = basePrice + serviceFee + vatFee

                          return (
                            <>
                              <div className="flex justify-between">
                                <p className="text-sm text-muted-foreground">
                                  {nightlyRate.toLocaleString()} TND × {nights} night{nights > 1 ? "s" : ""}
                                </p>
                                <p className="font-medium">{basePrice.toLocaleString()} TND</p>
                              </div>
                              <div className="flex justify-between">
                                <p className="text-sm text-muted-foreground">Frais de service (10%)</p>
                                <p className="font-medium">{serviceFee.toLocaleString()} TND</p>
                              </div>
                              <div className="flex justify-between">
                                <p className="text-sm text-muted-foreground">TVA (5%)</p>
                                <p className="font-medium">{vatFee.toLocaleString()} TND</p>
                              </div>
                              <div className="pt-2 border-t flex justify-between">
                                <p className="font-medium">Total</p>
                                <p className="font-medium">{total.toLocaleString()} TND</p>
                              </div>
                              <div className="flex justify-between text-muted-foreground">
                                <p className="text-sm">Paiement de sécurité</p>
                                <p className="text-sm">{securityFee.toLocaleString()} TND</p>
                              </div>
                            </>
                          )
                        })()}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-6 border-t">
                  <Button className="w-full bg-[#fa644c] hover:bg-[#fa644c]/90 text-white">
                    Envoyer un rappel de réservation
                  </Button>
                </div>
              </>
            )}
          </div>
        </SheetContent>
      </Sheet>

      {/* Blocked Date Sheet */}
      <Sheet open={isBlockedDateSheetOpen} onOpenChange={setIsBlockedDateSheetOpen}>
        <SheetContent className="w-[400px] sm:w-[540px]">
          <SheetHeader className="pb-4 border-b">
            <div className="flex items-center gap-2">
              <Lock className="h-5 w-5 text-[#fa644c]" />
              <SheetTitle>Dates bloquées</SheetTitle>
            </div>
            {selectedDate && (
              <p className="text-muted-foreground">
                Showing blocked listings for {format(selectedDate, "MMMM d, yyyy")}
              </p>
            )}
          </SheetHeader>

          <div className="py-6">
            <h3 className="text-lg font-medium mb-4">Listings bloqués ({selectedBlockedDates.length})</h3>

            <div className="space-y-4">
              {selectedBlockedDates.map((blockedDate) => (
                <Card key={blockedDate.id} className="p-4 border-[#fa644c]/20">
                  <div className="flex items-start gap-3">
                    <div className="bg-[#fa644c]/10 p-2 rounded-md">
                      <CalendarIcon className="h-5 w-5 text-[#fa644c]" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-[#fa644c]">{blockedDate.listing_title}</h4>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          <div className="pt-4 border-t mt-auto">
            <Button variant="outline" className="w-full" onClick={() => setIsBlockedDateSheetOpen(false)}>
              Fermer
            </Button>
          </div>
        </SheetContent>
      </Sheet>

      {/* Block/Unblock Dialog */}
      <Dialog open={blockDialog.open} onOpenChange={open => setBlockDialog({ ...blockDialog, open })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {blockDialog.action === 'block' ? 'Bloquer cette date ?' : 'Débloquer cette date ?'}
            </DialogTitle>
          </DialogHeader>
          <div className="py-4 text-center">
            {blockDialog.date && format(blockDialog.date, 'PPP', { locale: undefined })}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setBlockDialog({ ...blockDialog, open: false })}>Annuler</Button>
            <Button
              className="bg-[#fa644c] text-white"
              disabled={blockLoading}
              onClick={async () => {
                if (!blockDialog.date || !selectedListingId) return
                setBlockLoading(true)
                if (blockDialog.action === 'block') {
                  // POST to API
                  const dateStr = formatLocalDate(blockDialog.date)
                  const res = await fetch('/api/listing-availability', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ listing_id: selectedListingId, start_date: dateStr, end_date: dateStr })
                  })
                  if (res.ok) {
                    toast.success('Date bloquée !')
                    setBlockDialog({ open: false, date: null, action: 'block' })
                    await fetchBlockedDates()
                  } else {
                    toast.error('Erreur lors du blocage')
                  }
                } else if (blockDialog.action === 'unblock' && blockDialog.blockId) {
                  // DELETE to API
                  const res = await fetch(`/api/listing-availability?id=${blockDialog.blockId}`, { method: 'DELETE' })
                  if (res.ok) {
                    toast.success('Date débloquée !')
                    setBlockDialog({ open: false, date: null, action: 'block' })
                    await fetchBlockedDates()
                  } else {
                    toast.error('Erreur lors du déblocage')
                  }
                }
                setBlockLoading(false)
              }}
            >
              {blockLoading ? 'En cours...' : 'Confirmer'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// MonthView component - included in the same file
function MonthView({
  year,
  month,
  bookings,
  onBookingClick,
  onDayClick,
  blockedDates,
}: {
  year: number
  month: number
  bookings: Booking[]
  onBookingClick: (bookingId: string) => void
  onDayClick: (date: Date, isBlocked: boolean, blockId?: string) => void
  blockedDates: BlockedDate[]
}) {
  const daysInMonth = new Date(year, month + 1, 0).getDate()
  const firstDay = new Date(year, month, 1).getDay()

  const days = Array.from({ length: daysInMonth }, (_, i) => {
    const date = new Date(year, month, i + 1)
    const dayBookings = bookings.filter((booking) => {
      const bookingStart = new Date(booking.start_date)
      const bookingEnd = new Date(booking.end_date)
      bookingStart.setHours(0, 0, 0, 0)
      bookingEnd.setHours(0, 0, 0, 0)
      const currentDate = new Date(date)
      currentDate.setHours(0, 0, 0, 0)
      return currentDate >= bookingStart && currentDate <= bookingEnd
    })

    // Get all blocked dates for this day
    const dayBlockedDates = blockedDates.filter((blockedDate) => {
      const blockStart = new Date(blockedDate.start_date)
      const blockEnd = new Date(blockedDate.end_date)
      blockStart.setHours(0, 0, 0, 0)
      blockEnd.setHours(0, 0, 0, 0)
      const currentDate = new Date(date)
      currentDate.setHours(0, 0, 0, 0)
      return currentDate >= blockStart && currentDate <= blockEnd
    })

    // Check if the date is blocked
    const isBlocked = dayBlockedDates.length > 0

    // Check if any of the blocked dates start or end on this day
    const blockedDatesBoundary = dayBlockedDates.filter((blockedDate) => {
      const blockStart = new Date(blockedDate.start_date)
      const blockEnd = new Date(blockedDate.end_date)
      blockStart.setHours(0, 0, 0, 0)
      blockEnd.setHours(0, 0, 0, 0)
      const currentDate = new Date(date)
      currentDate.setHours(0, 0, 0, 0)
      return currentDate.getTime() === blockStart.getTime() || currentDate.getTime() === blockEnd.getTime()
    })

    // Is this day the start or end of any blocked period?
    const isBlockedBoundary = blockedDatesBoundary.length > 0

    // Find blockId if blocked
    const blockId = isBlocked && dayBlockedDates.length > 0 ? dayBlockedDates[0].id : undefined;

    return {
      day: i + 1,
      date,
      bookings: dayBookings,
      isBlocked,
      blockedDates: dayBlockedDates,
      isBlockedBoundary,
      blockedDatesBoundary,
    }
  })

  const previousMonthDays = Array.from({ length: firstDay }, (_, i) => ({
    day: new Date(year, month, 0).getDate() - i,
    date: new Date(year, month - 1, new Date(year, month, 0).getDate() - i),
    bookings: [],
    isBlocked: false,
    blockedDates: [],
    isBlockedBoundary: false,
    blockedDatesBoundary: [],
  })).reverse()

  const nextMonthDays = Array.from({ length: 42 - (firstDay + daysInMonth) }, (_, i) => ({
    day: i + 1,
    date: new Date(year, month + 1, i + 1),
    bookings: [],
    isBlocked: false,
    blockedDates: [],
    isBlockedBoundary: false,
    blockedDatesBoundary: [],
  }))

  const allDays = [...previousMonthDays, ...days, ...nextMonthDays]

  return (
    <div className="grid grid-cols-1 md:grid-cols-7 gap-[1px] bg-[#e8eaed]">
      <div className="hidden md:grid md:grid-cols-7 md:col-span-7 gap-[1px]">
        {DAYS.map((day) => (
          <div key={day} className="bg-[#fcfbff] p-4 text-sm font-medium text-[#4b4f5c]">
            {day}
          </div>
        ))}
      </div>

      {/* Mobile day headers */}
      <div className="grid grid-cols-7 col-span-1 gap-[1px] md:hidden sticky top-0 bg-[#fcfbff] z-10">
        {DAYS.map((day) => (
          <div key={day} className="p-2 text-xs font-medium text-[#4b4f5c] text-center">
            {day.charAt(0)}
          </div>
        ))}
      </div>

      {allDays.map(
        ({ day, date, bookings, isBlocked, blockedDates }, index) => {
          // Find blockId if blocked
          const blockId = isBlocked && blockedDates.length > 0 ? blockedDates[0].id : undefined;
          return (
            <button
              key={index}
              className={cn(
                "min-h-[100px] p-2 bg-white text-left",
                date.getMonth() !== month && "bg-[#fcfbff]",
                "md:min-h-[120px]",
                isBlocked && "border border-[#fa644c]/20",
                !isBlocked && bookings.length === 0 && "hover:bg-gray-100 cursor-pointer",
                bookings.length > 0 && "cursor-default"
              )}
              disabled={bookings.length > 0}
              onClick={() => {
                if (bookings.length === 0) onDayClick(date, isBlocked, blockId)
              }}
            >
              <div className="flex justify-between items-start">
                <span
                  className={cn(
                    "text-sm font-medium",
                    date.getMonth() !== month && "text-[#9197b3]",
                    date.getMonth() === month && "text-[#1a1a1a]",
                    isBlocked && "text-[#fa644c]",
                  )}
                >
                  {day}
                </span>
              </div>
              {isBlocked && date.getMonth() === month && (
                <div className="mt-2 flex items-center justify-center">
                  <Lock className="h-4 w-4 text-[#fa644c] mr-2" />
                  <span className="text-xs font-medium text-[#fa644c]">
                    {blockedDates.length > 1 ? `${blockedDates.length} Blocked` : "Blocked"}
                  </span>
                </div>
              )}
              <div className="mt-1 space-y-1">
                {bookings.map((booking) => (
                  <Card
                    key={booking.id}
                    className={cn(
                      "p-2 text-xs cursor-pointer hover:bg-gray-100",
                      isBlocked && "opacity-50 pointer-events-none",
                    )}
                    onClick={() => onBookingClick(booking.id)}
                  >
                    <div className="flex items-start gap-2">
                      <div
                        className={cn(
                          "w-1 h-1 rounded-full mt-1 flex-none",
                          booking.status?.toLowerCase() === "confirmed"
                            ? "bg-[#365df5]"
                            : booking.status?.toLowerCase() === "pending"
                              ? "bg-[#fa644c]"
                              : "bg-gray-300",
                        )}
                      />
                      <div className="min-w-0">
                        <p className="font-medium text-[#1a1a1a] truncate">
                          {booking.listing?.title || "Unnamed Listing"}
                        </p>
                        <p className="text-[#6c757e] truncate">{booking.user?.fullname || "Guest"}</p>
                        {date.getTime() === new Date(booking.start_date).setHours(0, 0, 0, 0) && (
                          <p className="text-green-600">Check-in</p>
                        )}
                        {date.getTime() === new Date(booking.end_date).setHours(0, 0, 0, 0) && (
                          <p className="text-red-600">Check-out</p>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </button>
          )
        }
      )}
    </div>
  )
}

// AnnualView component - included in the same file
function AnnualView({
  year,
  bookings,
  onBookingClick,
  onDayClick,
  blockedDates,
}: {
  year: number
  month: number
  bookings: Booking[]
  onBookingClick: (bookingId: string) => void
  onDayClick: (date: Date, isBlocked: boolean, blockId?: string) => void
  blockedDates: BlockedDate[]
}) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      {MONTHS.map((_, index) => (
        <div key={index} className="border rounded-lg">
          <MonthCalendar
            year={year}
            month={index}
            bookings={bookings}
            onBookingClick={onBookingClick}
            onDayClick={onDayClick}
            blockedDates={blockedDates}
          />
        </div>
      ))}
    </div>
  )
}

// MonthCalendar component - included in the same file
function MonthCalendar({
  year,
  month,
  bookings,
  onBookingClick,
  onDayClick,
  blockedDates,
}: {
  year: number
  month: number
  bookings: Booking[]
  onBookingClick: (bookingId: string) => void
  onDayClick: (date: Date, isBlocked: boolean, blockId?: string) => void
  blockedDates: BlockedDate[]
}) {
  const daysInMonth = new Date(year, month + 1, 0).getDate()
  const firstDay = new Date(year, month, 1).getDay()

  const days = Array.from({ length: daysInMonth }, (_, i) => {
    const date = new Date(year, month, i + 1)
    const dayBookings = bookings.filter((booking) => {
      const bookingStart = new Date(booking.start_date)
      const bookingEnd = new Date(booking.end_date)
      bookingStart.setHours(0, 0, 0, 0)
      bookingEnd.setHours(0, 0, 0, 0)
      const currentDate = new Date(date)
      currentDate.setHours(0, 0, 0, 0)
      return currentDate >= bookingStart && currentDate <= bookingEnd
    })

    // Get all blocked dates for this day
    const dayBlockedDates = blockedDates.filter((blockedDate) => {
      const blockStart = new Date(blockedDate.start_date)
      const blockEnd = new Date(blockedDate.end_date)
      blockStart.setHours(0, 0, 0, 0)
      blockEnd.setHours(0, 0, 0, 0)
      const currentDate = new Date(date)
      currentDate.setHours(0, 0, 0, 0)
      return currentDate >= blockStart && currentDate <= blockEnd
    })

    // Check if the date is blocked
    const isBlocked = dayBlockedDates.length > 0

    // Check if any of the blocked dates start or end on this day
    const blockedDatesBoundary = dayBlockedDates.filter((blockedDate) => {
      const blockStart = new Date(blockedDate.start_date)
      const blockEnd = new Date(blockedDate.end_date)
      blockStart.setHours(0, 0, 0, 0)
      blockEnd.setHours(0, 0, 0, 0)
      const currentDate = new Date(date)
      currentDate.setHours(0, 0, 0, 0)
      return currentDate.getTime() === blockStart.getTime() || currentDate.getTime() === blockEnd.getTime()
    })

    // Is this day the start or end of any blocked period?
    const isBlockedBoundary = blockedDatesBoundary.length > 0

    // Find blockId if blocked
    const blockId = isBlocked && dayBlockedDates.length > 0 ? dayBlockedDates[0].id : undefined;

    return {
      day: i + 1,
      date,
      bookings: dayBookings,
      isBlocked,
      blockedDates: dayBlockedDates,
      isBlockedBoundary,
      blockedDatesBoundary,
    }
  })

  const previousMonthDays = Array.from({ length: firstDay }, (_, i) => ({
    day: new Date(year, month, 0).getDate() - i,
    date: new Date(year, month - 1, new Date(year, month, 0).getDate() - i),
    bookings: [],
    isBlocked: false,
    blockedDates: [],
    isBlockedBoundary: false,
    blockedDatesBoundary: [],
  })).reverse()

  const nextMonthDays = Array.from({ length: 42 - (firstDay + daysInMonth) }, (_, i) => ({
    day: i + 1,
    date: new Date(year, month + 1, i + 1),
    bookings: [],
    isBlocked: false,
    blockedDates: [],
    isBlockedBoundary: false,
    blockedDatesBoundary: [],
  }))

  const allDays = [...previousMonthDays, ...days, ...nextMonthDays]

  return (
    <div className="p-2">
      <h3 className="text-sm font-medium mb-2">{MONTHS[month]}</h3>
      <div className="grid grid-cols-7 gap-1">
        {DAYS.map((day) => (
          <div key={day} className="text-xs text-center font-medium text-muted-foreground">
            {day.charAt(0)}
          </div>
        ))}
        {allDays.map(
          ({ day, date, bookings, isBlocked, blockedDates }, index) => {
            // Find blockId if blocked
            const blockId = isBlocked && blockedDates.length > 0 ? blockedDates[0].id : undefined;
            return (
              <button
                key={index}
                className={cn(
                  "relative text-xs p-1 rounded-sm w-full min-h-[40px] md:min-h-[60px]",
                  date.getMonth() !== month && "text-muted-foreground opacity-50 bg-[#fcfbff]",
                  isBlocked && "bg-[#fa644c]/10 text-[#fa644c] border border-[#fa644c]/20",
                  !isBlocked && bookings.length === 0 && "hover:bg-gray-100 cursor-pointer",
                  bookings.length > 0 && "cursor-default",
                  bookings.length > 0 &&
                    (bookings[0]?.status?.toLowerCase() === "confirmed"
                      ? "bg-[#365df5] text-white hover:bg-[#365df5]/90"
                      : bookings[0]?.status?.toLowerCase() === "pending"
                        ? "bg-[#fa644c] text-white hover:bg-[#fa644c]/90"
                        : "bg-gray-300"),
                )}
                disabled={bookings.length > 0}
                onClick={() => {
                  if (bookings.length === 0) onDayClick(date, isBlocked, blockId)
                }}
              >
                {day}
                {isBlocked && date.getMonth() === month && <Lock className="h-2 w-2 absolute bottom-0 right-0" />}
                {bookings.length > 0 && <span className="absolute bottom-0 right-0 w-2 h-2 bg-white rounded-full" />}
              </button>
            );
          }
        )}
      </div>
    </div>
  )
}
