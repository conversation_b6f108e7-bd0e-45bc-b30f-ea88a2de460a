"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  ArrowLeft,
  Edit,
  Plus,
  Wifi,
  Bath,
  Wind,
  Droplets,
  Car,
  Tv,
  Camera,
  Gamepad2,
  MessageSquare,
  Bell,
  Search,
  MoreVertical,
} from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"

interface ListingDetails {
  propertyId: string
  propertyTitle: string
  propertyDescription: string
  propertyType: string
  propertyCategory: string
  propertyState: string
  propertyCity: string
  propertyAddress: string
  numGuests: number
  numBedrooms: number
  numBeds: number
  numBathrooms: number
  coverImageUrl: string
  placeImagesUrl: string[]
  pricing: {
    nightlyRate: number
    minStay: number
    maxStay: number | null
    additionalFees: number | null
  }
  hostId: string
  status: string
  amenities: {
    id: string
    name: string
    icon: string
  }[]
}

export default function ListingDetailsPage() {


  const params = useParams()
  const [listingDetails, setListingDetails] = useState<ListingDetails | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const router = useRouter()

  useEffect(() => {
    const fetchListingDetails = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/listing_details/${params.ID}`)
        if (!response.ok) {
          throw new Error("Échec de la récupération des détails de l'annonce")
        }
        const data = await response.json()
        if (data.success && data.listing) {
          console.log(data.listing)
          setListingDetails(data.listing)
          setSelectedImage(data.listing.coverImageUrl)
        } else {
          throw new Error(data.error || "Échec de la récupération des détails de l'annonce")
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Une erreur s'est produite")
        console.error("Erreur lors de la récupération des détails de l'annonce:", err)
      } finally {
        setIsLoading(false)
      }
    }

    if (params.ID) {
      fetchListingDetails()
    }
  }, [params.ID])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#fcfbff]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#365df5]"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#fcfbff] p-6 flex items-center justify-center">
        <div className="text-red-500 text-xl">Erreur : {error}</div>
      </div>
    )
  }

  if (!listingDetails) {
    return (
      <div className="min-h-screen bg-[#fcfbff] p-6 flex items-center justify-center">
        <div className="text-[#64748b] text-xl">Aucun détail d&apos;annonce trouvé</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#fcfbff]">
      <header className="border-b bg-white sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                href="/dashboard/properties/listings"
                className="flex items-center gap-2 text-[#32353e]"
              >
                <ArrowLeft className="w-5 h-5" />
                <span className="text-xl font-semibold">Mes propriétés</span>
              </Link>
              <h1 className="text-xl font-semibold">{listingDetails.propertyTitle}</h1>
            </div>
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-4"></div>
              <div className="h-8 w-[1px] bg-[#e8eaed]" />
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  className="flex items-center gap-2"
                  onClick={() => {
                    const queryParams = new URLSearchParams({
                      data: JSON.stringify(listingDetails),
                    }).toString()
                    router.push(`/dashboard/properties/listings/${params.ID}/edit?${queryParams}`)
                  }}
                >
                  <Edit className="w-4 h-4" />
                  Modifier la chambre
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-4">
            <div className="relative h-[400px] rounded-xl overflow-hidden bg-[#e8eaed]">
              <Image
                src={selectedImage || listingDetails.coverImageUrl || "/placeholder.svg"}
                alt={listingDetails.propertyTitle}
                fill
                className="object-cover"
              />
            </div>
            <div className="grid grid-cols-4 gap-4">
              {listingDetails.placeImagesUrl.slice(0, 4).map((image, index) => (
                <div
                  key={index}
                  className="relative h-24 rounded-xl overflow-hidden bg-[#e8eaed] cursor-pointer"
                  onClick={() => setSelectedImage(image)}
                >
                  <Image
                    src={image || "/placeholder.svg"}
                    alt={`Image de la galerie ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full py-6">
              Télécharger une nouvelle photo
            </Button>
          </div>

          <div className="space-y-6">
            <Card className="bg-white rounded-xl shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-baseline justify-between mb-6">
                  <h2 className="text-3xl font-bold">{listingDetails.pricing.nightlyRate} TND</h2>
                  <span className="text-[#64748b]">\Nuit</span>
                </div>

                <div className="grid grid-cols-3 gap-6 mb-8">
                  <div>
                    <h3 className="text-sm font-medium mb-2">Statut de l&apos;annonce</h3>
                    <Badge variant="outline" className="rounded-full">
                      {listingDetails.status}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Capacité</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center gap-2">
                        <span className="text-[#64748b]">Invités :</span>
                        <span>{listingDetails.numGuests}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-[#64748b]">Chambres :</span>
                        <span>{listingDetails.numBedrooms}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-[#64748b]">Lits :</span>
                        <span>{listingDetails.numBeds}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-[#64748b]">Salles de bain :</span>
                        <span>{listingDetails.numBathrooms}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-4">Équipements</h3>
                    <div className="grid grid-cols-3 gap-4">
                      {listingDetails.amenities.map((facility, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm text-[#64748b]">
                          <span>{facility.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}