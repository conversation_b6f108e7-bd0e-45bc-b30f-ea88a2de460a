"use client"

import React, {useEffect} from "react"
import { useState, useCallback } from "react"
import TypeOfPlaceFilter, { type FilterOption } from "./(components)/TypeOfPlaceFilter"
import RoomAndBedsFilter from "./(components)/RoomAndBedsFilter"
import PriceRangeFilter from "./(components)/PriceRangeFilter"
import MoreFiltersModal from "./(components)/MoreFiltersModal"
import MobileFilters from "./(components)/MobileFilters"
import { useSearch, type SearchParams } from "./SearchContext"
import { Switch } from "@headlessui/react"
import { DollarSign, Zap } from "lucide-react"
import Image from "next/image"

interface TabFiltersProps {
    className?: string
    onFiltersChange: (filters: SearchParams) => void;
    desktopMap?: boolean;                // For desktop toggle
    onToggleDesktopMap?: (show: boolean) => void;
    mobileMap?: boolean;                 // For mobile toggle
    onToggleMobileMap?: (show: boolean) => void;
}

interface Filters {
    propertyTypes: FilterOption[]
    propertyCategories: FilterOption[]
    amenities: FilterOption[]
    roomTypes: FilterOption[]
}

const TabFilters: React.FC<TabFiltersProps> = ({
                                                   onFiltersChange,
                                                   desktopMap,
                                                   onToggleDesktopMap,
                                                   mobileMap,
                                                   onToggleMobileMap,
    className
                                               }) => {
    const { searchParams, setSearchParams } = useSearch()
    const [isOpenMoreFilter, setIsOpenMoreFilter] = useState(false)
    const [isOpenMobileFilter, setIsOpenMobileFilter] = useState(false)
    const [filters, setFilters] = useState<Filters>({
        propertyTypes: [],
        propertyCategories: [],
        amenities: [],
        roomTypes: [],
    })
    const [isLoading, setIsLoading] = useState(true)

    useEffect(() => {
        // Fetch filters from API
        const fetchFilters = async () => {
            setIsLoading(true)
            try {
                const response = await fetch("/api/filters")
                if (response.ok) {
                    const data = await response.json()
                    setFilters(data)
                } else {
                    console.error("Failed to fetch filters")
                }
            } catch (error) {
                console.error("Error fetching filters:", error)
            } finally {
                setIsLoading(false)
            }
        }

        fetchFilters()
    }, [])

    const handleFilterChange = useCallback(
        (filterType: keyof SearchParams, value: any) => {
            setSearchParams((prevParams) => {
                const newParams = { ...prevParams, [filterType]: value }
                onFiltersChange(newParams)
                return newParams
            })
        },
        [setSearchParams, onFiltersChange],
    )

    // Handler for category selection
    const handleCategoryChange = useCallback(
        (categoryId: string) => {
            setSearchParams((prevParams) => {
                const newCategories = prevParams.propertyCategories.includes(categoryId)
                    ? prevParams.propertyCategories.filter((id) => id !== categoryId)
                    : [...prevParams.propertyCategories, categoryId]

                const newParams = { ...prevParams, propertyCategories: newCategories }
                return newParams
            })
        },
        [setSearchParams],
    )

    const handleRoomAndBedsChange = useCallback(
        (filters: Partial<Pick<SearchParams, "beds" | "bedrooms" | "bathrooms" | "kitchens">>) => {
            setSearchParams((prevParams) => {
                const newParams = { ...prevParams, ...filters }
                onFiltersChange(newParams)
                return newParams
            })
        },
        [setSearchParams, onFiltersChange],
    )

    // Clear functions for different buttons
    const clearMoreFilters = () => {
        handleFilterChange("propertyCategories", [])
        handleFilterChange("amenities", [])
        handleFilterChange("roomTypes", [])
        handleFilterChange("minRating", null)
    }

    const clearMobileFilters = () => {
        handleFilterChange("propertyTypes", [])
        handleFilterChange("propertyCategories", [])
        handleFilterChange("amenities", [])
        handleFilterChange("roomTypes", [])
        handleFilterChange("priceRange", [0, 1000])
        handleFilterChange("minRating", null)
        handleRoomAndBedsChange({ beds: 0, bedrooms: 0, bathrooms: 0, kitchens: 0 })
    }

    // Updated renderXClear accepts a clear callback
    const renderXClear = (clearFn: () => void) => {
        return (
            <span
                onClick={(e) => {
                    e.stopPropagation()
                    clearFn()
                }}
                className="ml-3 flex h-4 w-4 cursor-pointer items-center justify-center rounded-full bg-booking-orange text-white"
            >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
          <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
          />
        </svg>
      </span>
        )
    }

    const isAnyFilterSelected =
        searchParams.propertyCategories.length + searchParams.amenities.length + searchParams.roomTypes.length > 0

    const isMobileFilterSelected =
        searchParams.propertyTypes.length +
        (searchParams.bedrooms > 1 ? 1 : 0) +
        (searchParams.bathrooms > 1 ? 1 : 0) +
        (searchParams.bedrooms > 1 ? 1 : 0) +
        (searchParams.beds > 1 ? 1 : 0) +
        (searchParams.kitchens > 1 ? 1 : 0) +
        searchParams.propertyCategories.length +
        searchParams.amenities.length +
        searchParams.roomTypes.length +
        (searchParams.minRating ? 1 : 0) >
        0

    return (
        <div className={className}>
            <div className="px-4 xl:px-10 2xl:px-20">
            <div className="flex flex-col gap-4 max-w-lg md:max-w-full mx-auto">
                <div className="flex justify-between items-center">
                    <div className="hidden space-x-4 lg:flex">
                        <TypeOfPlaceFilter
                            filters={filters}
                            selectedFilters={{ propertyTypes: searchParams.propertyTypes || [] }}
                            applyFilters={(_, value) => handleFilterChange("propertyTypes", value)}
                            clearFilters={() => handleFilterChange("propertyTypes", [])}
                        />
                        <PriceRangeFilter
                            priceRange={searchParams.priceRange}
                            setPriceRange={(newRange) => handleFilterChange("priceRange", newRange)}
                            clearFilters={() => handleFilterChange("priceRange", [0, 1000])}
                            applyFilters={(filters) => handleFilterChange("priceRange", filters.priceRange)}
                        />
                        <RoomAndBedsFilter
                            beds={searchParams.beds}
                            bedrooms={searchParams.bedrooms}
                            bathrooms={searchParams.bathrooms}
                            kitchens={searchParams.kitchens}
                            handleRoomChange={(type, value) => handleFilterChange(type, value)}
                            clearFilters={() => {
                                handleRoomAndBedsChange({ beds: 0, bedrooms: 0, bathrooms: 0, kitchens: 0 })
                            }}
                            applyFilters={handleRoomAndBedsChange}
                        />
                        <div>
                            <button
                                className={`flex items-center justify-center rounded-full border border-neutral-300 px-3 py-2 text-sm hover:border-neutral-400 focus:outline-none dark:border-neutral-700 dark:hover:border-neutral-600
                                ${isAnyFilterSelected ? "bg-light-orange text-booking-orange !border-booking-orange" : ""}
                                `}
                                onClick={() => setIsOpenMoreFilter(true)}
                            >
                                <span>
                                  Plus de filtres{" "}
                                    {isAnyFilterSelected
                                        ? `(${searchParams.propertyCategories.length + searchParams.amenities.length + searchParams.roomTypes.length})`
                                        : ""}
                                </span>
                                {isAnyFilterSelected ? renderXClear(clearMoreFilters) : <i className="las la-angle-down ml-2"></i>}
                            </button>
                        </div>

                        <div>
                            <button
                                onClick={() =>
                                    handleFilterChange("paymentType", searchParams.paymentType === "Cashbased" ? null : "Cashbased")
                                }
                                className={`flex items-center justify-center rounded-full border border-neutral-300 px-3 py-2 gap-2 text-sm hover:border-neutral-400 focus:outline-none dark:border-neutral-700 dark:hover:border-neutral-600
                          ${searchParams.paymentType === "Cashbased" ? "bg-light-orange text-booking-orange !border-booking-orange" : ""}`}
                            >
                                <span>Paiement en espèces</span>
                                <DollarSign className="w-4 h-4" />
                            </button>
                        </div>

                        <div>
                            <button
                                onClick={() =>
                                    handleFilterChange("paymentType", searchParams.paymentType === "Instant" ? null : "Instant")
                                }
                                className={`flex items-center justify-center rounded-full border border-neutral-300 px-3 py-2 gap-2 text-sm hover:border-neutral-400 focus:outline-none dark:border-neutral-700 dark:hover:border-neutral-600
                          ${searchParams.paymentType === "Instant" ? "bg-light-orange text-booking-orange !border-booking-orange" : ""}`}
                            >
                                <span>Réservation instantanée</span>
                                <Zap className="w-4 h-4" />
                            </button>
                        </div>

                        {/* Map Toggle Switch for Desktop */}
                        <div className="flex items-center justify-center rounded-full border border-neutral-300 px-3 py-2 gap-2 text-sm hover:border-neutral-400 focus:outline-none dark:border-neutral-700 dark:hover:border-neutral-600">
                            <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6.125 4.34409V10.8753M10.875 6.12534V12.6566M11.2733 15.4262L15.1327 13.4965C15.4344 13.3456 15.625 13.0373 15.625 12.6999V2.81641C15.625 2.15433 14.9283 1.72372 14.3361 2.01981L11.2733 3.55119C11.0226 3.67656 10.7274 3.67656 10.4767 3.55119L6.5233 1.57449C6.27256 1.44913 5.97744 1.44913 5.7267 1.57449L1.86733 3.50418C1.5656 3.65505 1.375 3.96344 1.375 4.30078V14.1843C1.375 14.8464 2.07175 15.277 2.66392 14.9809L5.7267 13.4495C5.97744 13.3241 6.27256 13.3241 6.5233 13.4495L10.4767 15.4262C10.7274 15.5516 11.0226 15.5516 11.2733 15.4262Z" stroke="#050760" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                            <span>Carte</span>
                            <Switch
                                checked={desktopMap}                    // Use desktopMap here
                                onChange={onToggleDesktopMap}            // Call the desktop toggle handler
                                className={`${desktopMap ? "bg-booking-orange" : "bg-gray-200"} relative inline-flex h-5 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-booking-orange focus:ring-offset-2`}
                            >
                                <span className="sr-only">Show map</span>
                                <span
                                    className={`
                                    ${desktopMap ? "translate-x-6" : "translate-x-1"
                                    } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                                />
                            </Switch>
                        </div>
                    </div>

                    <div className="flex lg:hidden">
                        <button
                            onClick={() => setIsOpenMobileFilter(true)}
                            className={`flex z-50 items-center justify-center rounded-full border border-neutral-300 px-3 py-2 text-sm hover:border-neutral-400 focus:outline-none dark:border-neutral-700 dark:hover:border-neutral-600 ${isMobileFilterSelected ? "bg-light-orange text-booking-orange !border-booking-orange" : "bg-white"}`}
                        >
                            <i className="las la-sliders-h mr-2 text-[18px]"></i>
                              <span className="">
                                Filtres{" "}
                                  {isMobileFilterSelected
                                      ? `(${searchParams.propertyTypes.length + (searchParams.bedrooms > 1 ? 1 : 0) + (searchParams.bathrooms > 1 ? 1 : 0) + (searchParams.bedrooms > 1 ? 1 : 0) + (searchParams.beds > 1 ? 1 : 0) + searchParams.propertyCategories.length + searchParams.amenities.length + searchParams.roomTypes.length})`
                                      : ""}
                              </span>
                            {isMobileFilterSelected && renderXClear(clearMobileFilters)}
                        </button>
                    </div>
                </div>
                <MoreFiltersModal
                    isOpen={isOpenMoreFilter}
                    onClose={() => setIsOpenMoreFilter(false)}
                    filters={filters}
                    selectedFilters={{
                        amenities: searchParams.amenities || [],
                        roomTypes: searchParams.roomTypes || [],
                        minRating:   searchParams.minRating  ?? null,
                    }}
                    clearFilters={clearMoreFilters}
                    applyFilters={(newFilters) => {
                        handleFilterChange("amenities", newFilters.amenities)
                        handleFilterChange("roomTypes", newFilters.roomTypes)
                        handleFilterChange("minRating",   newFilters.minRating ?? null)
                        setIsOpenMoreFilter(false)
                    }}
                />
                <MobileFilters
                    isOpen={isOpenMobileFilter}
                    onClose={() => setIsOpenMobileFilter(false)}
                    filters={filters}
                    selectedFilters={{
                        propertyTypes: searchParams.propertyTypes || [],
                        propertyCategories: searchParams.propertyCategories || [],
                        amenities: searchParams.amenities || [],
                        roomTypes: searchParams.roomTypes || [],
                    }}
                    clearFilters={clearMobileFilters}
                    applyFilters={(newFilters) => {
                        handleFilterChange("propertyTypes", newFilters.propertyTypes)
                        handleFilterChange("amenities", newFilters.amenities)
                        handleFilterChange("roomTypes", newFilters.roomTypes)
                        setIsOpenMobileFilter(false)
                    }}
                    priceRange={searchParams.priceRange}
                    setPriceRange={(newRange) => handleFilterChange("priceRange", newRange)}
                    beds={searchParams.beds}
                    bedrooms={searchParams.bedrooms}
                    bathrooms={searchParams.bathrooms}
                    handleRoomChange={(type, value) => handleFilterChange(type, value)}
                    categoriesDisplay={mobileMap || false}
                />
            </div>
            </div>
        </div>
    )
}

export default TabFilters

