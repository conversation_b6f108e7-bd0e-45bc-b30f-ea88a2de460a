"use server"

import { createClient } from "@/utils/supabase/server"
import { normalizeDate } from "@/utils/dateUtils";

// Helper to calculate discount
function calculateDiscount(numberOfNights: number, discounts: any): number {
    let discountAmount = 0;
    if (!discounts || numberOfNights <= 0) return 0;
    if (Array.isArray(discounts)) {
        const applicableDiscounts = discounts.filter((d) => numberOfNights >= d.min_period);
        if (applicableDiscounts.length > 0) {
            const bestDiscount = applicableDiscounts.sort((a, b) => b.discount_rate - a.discount_rate)[0];
            discountAmount = bestDiscount.discount_rate;
        }
    } else {
        if (numberOfNights >= discounts.min_period) {
            discountAmount = discounts.discount_rate;
        }
    }
    return discountAmount;
}

// Helper to generate a random 8-character alphanumeric string
function generateReservationNumber() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

type BookingResponse = {
    success: boolean;
    error?: string;
    data?: {
        id: string;
    };
}

// Define the fee type
type AdditionalFee = {
  amount: number;
  title?: string;
  // Add other properties as needed
};

export async function createBookingRequest(bookingData: {
    listingId: string
    startDate: Date
    endDate: Date
    numGuests: number
    hostId: string
    totalPrice: number
}): Promise<BookingResponse> {
    const supabase = await createClient()

    //Check if user is authenticated
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (!user || userError) {
        return {
            success: false,
            error: "You must be logged in to make a booking request"
        }
    }

    try {
        // Fetch listing to ensure it exists
        const { data: listing, error: listingError } = await supabase
            .from('listings')
            .select('id, host_id, payment_type')
            .eq('id', bookingData.listingId)
            .single();
        if (listingError || !listing) {
            throw new Error("Listing not found or error fetching listing data");
        }

        // Fetch pricing info from listing_pricing
        const { data: pricing, error: pricingError } = await supabase
            .from('listing_pricing')
            .select('nightly_rate, additional_fees, discounts')
            .eq('listing_id', bookingData.listingId)
            .single();
        if (pricingError || !pricing) {
            throw new Error("Pricing not found for this listing");
        }

        // Calculate nights
        const nights = Math.ceil((bookingData.endDate.getTime() - bookingData.startDate.getTime()) / (1000 * 60 * 60 * 24));
        
        // Calculate base price
        const nightsTotal = nights * pricing.nightly_rate;
        
        // Calculate discount
        const discountRate = calculateDiscount(nights, pricing.discounts);
        const discountAmount = (discountRate / 100) * nightsTotal;
        
        // Calculate additional fees
        let totalAdditionalFees = 0;
        if (pricing.additional_fees) {
            totalAdditionalFees = pricing.additional_fees.reduce((sum: number, fee: AdditionalFee) => sum + fee.amount, 0);
        }
        
        // Calculate subtotal (base price - discount + additional fees)
        const subtotal = (nightsTotal - discountAmount) + totalAdditionalFees;
        
        // Only apply service fee for Instant payment type
        const serviceFee = listing.payment_type === "Instant" ? Math.round(subtotal * 0.07) : 0;
        
        // Final total price the guest pays
        const finalTotal = subtotal + serviceFee;

        // Generate a unique reservation_number
        let reservationNumber: string;
        let isUnique = false;
        let attempts = 0;
        do {
            reservationNumber = generateReservationNumber();
            const { data: existing, error: checkError } = await supabase
                .from('bookings')
                .select('id')
                .eq('reservation_number', reservationNumber)
                .maybeSingle();
            if (!existing) isUnique = true;
            attempts++;
            if (attempts > 10) throw new Error('Failed to generate unique reservation number');
        } while (!isUnique);

        const { data, error } = await supabase
            .from('bookings')
            .insert({
                listing_id: bookingData.listingId,
                user_id: user.id,
                host_id: bookingData.hostId,
                start_date: normalizeDate(bookingData.startDate),
                end_date: normalizeDate(bookingData.endDate),
                num_guests: bookingData.numGuests,
                total_price: finalTotal,
                status: 'pending',
                reservation_number: reservationNumber,
            })
            .select('id')
            .single();

        if (error) throw error;

        return {
            success: true,
            data: {
                id: data.id
            },
        };
    } catch (error: any) {
        console.error('Booking creation error:', error)
        return {
            success: false,
            error: error.message
        }
    }
}
