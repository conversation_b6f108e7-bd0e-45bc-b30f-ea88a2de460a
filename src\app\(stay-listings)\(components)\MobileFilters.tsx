"use client"

import React, { useState, useCallback, useEffect } from "react"
import { Dialog, Transition } from "@headlessui/react"
import ButtonPrimary from "@/shared/ButtonPrimary"
import ButtonThird from "@/shared/ButtonThird"
import ButtonClose from "@/shared/ButtonClose"
import Checkbox from "@/shared/Checkbox"
import Slider from "rc-slider"
import NcInputN<PERSON>ber from "@/components/NcInputNumber"
import type { FilterOption } from "./MoreFiltersModal"

interface Filters {
    propertyTypes: FilterOption[]
    propertyCategories: FilterOption[]
    amenities: FilterOption[]
    roomTypes: FilterOption[]
}

interface SelectedFilters {
    propertyTypes: string[]
    propertyCategories: string[]
    amenities: string[]
    roomTypes: string[]
}

interface MobileFiltersProps {
    isOpen: boolean
    onClose: () => void
    filters: Filters
    selectedFilters: SelectedFilters
    applyFilters: (filters: SelectedFilters) => void
    clearFilters: () => void
    priceRange: [number, number]
    setPriceRange: (range: [number, number]) => void
    beds: number
    bedrooms: number
    bathrooms: number
    handleRoomChange: (type: "beds" | "bedrooms" | "bathrooms", value: number) => void
    categoriesDisplay: boolean
}

const MobileFilters: React.FC<MobileFiltersProps> = ({
                                                         isOpen,
                                                         onClose,
                                                         filters,
                                                         selectedFilters,
                                                         applyFilters,
                                                         clearFilters,
                                                         priceRange,
                                                         setPriceRange,
                                                         beds,
                                                         bedrooms,
                                                         bathrooms,
                                                         handleRoomChange,
                                                         categoriesDisplay
                                                     }) => {
    const [localFilters, setLocalFilters] = useState<SelectedFilters>(selectedFilters)
    const [localPriceRange, setLocalPriceRange] = useState<[number, number]>(priceRange)
    const [localBeds, setLocalBeds] = useState(beds)
    const [localBedrooms, setLocalBedrooms] = useState(bedrooms)
    const [localBathrooms, setLocalBathrooms] = useState(bathrooms)

    useEffect(() => {
        if (isOpen) {
            setLocalFilters({ ...selectedFilters })
            setLocalPriceRange(priceRange)
            setLocalBeds(beds)
            setLocalBedrooms(bedrooms)
            setLocalBathrooms(bathrooms)
        }
    }, [isOpen, selectedFilters, priceRange, beds, bedrooms, bathrooms])

    const handleLocalFilterChange = useCallback((filterType: keyof SelectedFilters, id: string, checked: boolean) => {
        setLocalFilters((prev) => ({
            ...prev,
            [filterType]: checked ? [...prev[filterType], id] : prev[filterType].filter((item) => item !== id),
        }))
    }, [])

    const handleClearFilters = useCallback(() => {
        setLocalFilters({ propertyTypes: [], propertyCategories: [], amenities: [], roomTypes: [] })
        setLocalPriceRange([0, 1000])
        setLocalBeds(0)
        setLocalBedrooms(0)
        setLocalBathrooms(0)
        clearFilters()
    }, [clearFilters])

    const handleApplyFilters = useCallback(() => {
        applyFilters({ ...localFilters })
        setPriceRange(localPriceRange)
        handleRoomChange("beds", localBeds)
        handleRoomChange("bedrooms", localBedrooms)
        handleRoomChange("bathrooms", localBathrooms)
        onClose()
    }, [
        localFilters,
        localPriceRange,
        localBeds,
        localBedrooms,
        localBathrooms,
        applyFilters,
        setPriceRange,
        handleRoomChange,
        onClose,
    ])

    const renderMoreFilterItem = (filterType: keyof Filters, data: FilterOption[], filterTitle: string) => {
        return (
            <div className="py-7">
                <h3 className="text-xl font-medium">{filterTitle}</h3>
                <div className="relative mt-6">
                    <div className="grid grid-cols-2 gap-8">
                        <div className="flex flex-col space-y-5">
                            {data.slice(0, Math.ceil(data.length / 2)).map((item) => (
                                <Checkbox
                                    key={item.id}
                                    name={item.name}
                                    label={item.name}
                                    checked={localFilters[filterType].includes(item.id)}
                                    onChange={(checked) => handleLocalFilterChange(filterType, item.id, checked)}
                                />
                            ))}
                        </div>
                        <div className="flex flex-col space-y-5">
                            {data.slice(Math.ceil(data.length / 2)).map((item) => (
                                <Checkbox
                                    key={item.id}
                                    name={item.name}
                                    label={item.name}
                                    checked={localFilters[filterType].includes(item.id)}
                                    onChange={(checked) => handleLocalFilterChange(filterType, item.id, checked)}
                                />
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <Transition show={isOpen} as={React.Fragment}>
            <Dialog as="div" className="fixed inset-0 z-50 overflow-y-auto" onClose={onClose}>
                <div className="min-h-screen text-center">
                    <Transition.Child
                        as={React.Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <div className="fixed inset-0 bg-black bg-opacity-40 dark:bg-opacity-60" />
                    </Transition.Child>

                    <Transition.Child
                        as={React.Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0 scale-95"
                        enterTo="opacity-100 scale-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100 scale-100"
                        leaveTo="opacity-0 scale-95"
                    >
                        <div className="inline-block max-w-md p-6 m-4 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
                            <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900 mb-4">
                                Filtres
                            </Dialog.Title>
                            <ButtonClose className="absolute right-3 top-3" onClick={onClose} />

                            <div className="mt-4 space-y-6">
                                {renderMoreFilterItem("propertyTypes", filters.propertyTypes, "Types de propriété")}

                                {filters.roomTypes &&
                                    filters.roomTypes.length > 0 &&
                                    renderMoreFilterItem("roomTypes", filters.roomTypes, "Type de chambre")}

                                <div className="py-7">
                                    <h3 className="text-xl font-medium">Plage de prix</h3>
                                    <div className="relative mt-6">
                                        <Slider
                                            range
                                            min={0}
                                            max={1000}
                                            value={localPriceRange}
                                            onChange={(value) => setLocalPriceRange(value as [number, number])}
                                        />
                                    </div>
                                    <div className="mt-4 flex justify-between">
                                        <span>${localPriceRange[0]}</span>
                                        <span>${localPriceRange[1]}</span>
                                    </div>
                                </div>

                                <div className="py-7">
                                    <h3 className="text-xl font-medium mb-4">Chambres et lits</h3>
                                    <div className="space-y-4">
                                        <NcInputNumber label="Lits" max={10} onChange={setLocalBeds} defaultValue={localBeds} />
                                        <NcInputNumber label="Chambres" max={10} onChange={setLocalBedrooms} defaultValue={localBedrooms} />
                                        <NcInputNumber
                                            label="Salles de bain"
                                            max={10}
                                            onChange={setLocalBathrooms}
                                            defaultValue={localBathrooms}
                                        />
                                    </div>
                                </div>

                                {renderMoreFilterItem("amenities", filters.amenities, "Équipements")}
                                {categoriesDisplay && renderMoreFilterItem("propertyCategories", filters.propertyCategories, "Catégorie de propriété")}
                            </div>

                            <div className="mt-8 flex justify-between">
                                <ButtonThird onClick={handleClearFilters} sizeClass="px-4 py-2 sm:px-5">
                                    Effacer
                                </ButtonThird>
                                <ButtonPrimary onClick={handleApplyFilters} sizeClass="px-4 py-2 sm:px-5">
                                    Appliquer
                                </ButtonPrimary>
                            </div>
                        </div>
                    </Transition.Child>
                </div>
            </Dialog>
        </Transition>
    )
}

export default MobileFilters

