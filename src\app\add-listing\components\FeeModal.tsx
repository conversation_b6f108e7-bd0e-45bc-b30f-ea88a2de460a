import * as React from "react";
import { Dialog, DialogContent, } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {useState, useEffect} from "react";
import { X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

/**
 * FeeModalProps interface
 * -----------------------
 * Matches your original logic: we have title, price, feeType, isOptional, editingIndex,
 * plus callbacks for onConfirm and onClose.
 */
interface FeeModalProps {
    feeModalData: {
        title: string;
        price: number;
        feeType: string;
        editingIndex: number | null;
    };
    setFeeModalData: React.Dispatch<
        React.SetStateAction<{
            title: string;
            price: number;
            feeType: string;
            editingIndex: number | null;
        }>
    >;
    onConfirm: () => void;
    onClose: () => void;
    /**
     * If you're conditionally rendering this component, you can pass `open = true/false`.
     * For forwardRef usage, you can also pass `ref` if needed.
     * Or just omit if you prefer a simpler approach.
     */
    open: boolean;
    /**
     * If false, title is not editable and always set to Nettoyage.
     * If true (default), title is editable.
     */
    titleEditable?: boolean;
    feeTitleError?: string;
}

// Animation variants for mobile modal
const mobileModalVariants = {
    hidden: { y: '100%', opacity: 0 },
    visible: {
        y: 0,
        opacity: 1,
        transition: {
            type: "spring",
            stiffness: 500,
            damping: 35,
            mass: 0.8
        }
    },
    exit: {
        y: '100%',
        opacity: 0,
        transition: {
            type: "spring",
            stiffness: 500,
            damping: 35,
            mass: 0.8
        }
    },
};

/**
 * A FeeModal using shadcn/ui's Dialog with mobile-responsive animations.
 * We forward the ref in case you want to reference the modal from a parent.
 */
const FeeModal = React.forwardRef<HTMLDivElement, FeeModalProps>((props, ref) => {
    const { feeModalData, setFeeModalData, onConfirm, onClose, open, titleEditable = true, feeTitleError } = props;
    const { title, price, feeType } = feeModalData;
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [isFocused, setIsFocused] = useState(false);

    // Disable body scroll when modal is open
    useEffect(() => {
        if (open) {
            // Save current scroll position
            const scrollY = window.scrollY;
            document.body.style.position = 'fixed';
            document.body.style.top = `-${scrollY}px`;
            document.body.style.width = '100%';
        } else {
            // Restore scroll position
            const scrollY = document.body.style.top;
            document.body.style.position = '';
            document.body.style.top = '';
            document.body.style.width = '';
            if (scrollY) {
                window.scrollTo(0, parseInt(scrollY || '0') * -1);
            }
        }

        // Cleanup function to restore scroll when component unmounts
        return () => {
            document.body.style.position = '';
            document.body.style.top = '';
            document.body.style.width = '';
        };
    }, [open]);

    // Helper to update fields in feeModalData
    const handleChange = (field: keyof FeeModalProps["feeModalData"], value: unknown) => {
        setFeeModalData((prev) => ({ ...prev, [field]: value }));
        setErrors((prev) => ({ ...prev, [field]: "" }));
    };

    const validateForm = () => {
        const newErrors: Record<string, string> = {};
        if (!feeType || feeType === "none") {
            newErrors.feeType = "Le type de frais est requis";
        }
        if (!price || price <= 0) {
            newErrors.price = "Le prix doit être supérieur à 0";
        }
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleConfirm = () => {
        if (validateForm()) {
            onConfirm();
        }
    };

    // If not editable, always set Nettoyage as title
    const displayTitle = titleEditable ? title : "Nettoyage";

    return (
        <AnimatePresence>
            {open && (
                <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
                    {/* Custom backdrop */}
                    <motion.div
                        className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm z-40"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.2, ease: "easeOut" }}
                        onClick={onClose}
                    />

                    {/* Mobile-responsive modal content */}
                    <div className="fixed inset-0 z-50 flex items-end sm:items-center sm:justify-center">
                        <motion.div
                            ref={ref}
                            variants={mobileModalVariants}
                            initial="hidden"
                            animate="visible"
                            exit="exit"
                            className="w-full max-w-md mx-auto bg-white rounded-t-2xl sm:rounded-2xl shadow-xl overflow-hidden sm:relative sm:max-h-[90vh]"
                        >
                            {/* Modal Header */}
                            <div className="flex items-center justify-between px-6 pt-6 pb-2">
                                {titleEditable ? (
                                    <input
                                        type="text"
                                        value={displayTitle || ""}
                                        onChange={e => handleChange("title", e.target.value)}
                                        onFocus={() => setIsFocused(true)}
                                        onBlur={() => setIsFocused(false)}
                                        style={{ borderBottomColor: isFocused ? '#EA580F' : undefined, boxShadow: isFocused ? 'none' : undefined }}
                                        className="w-full max-w-[260px] mr-4 h-12 px-4 text-base font-semibold text-neutral-900 border-0 border-b border-b-neutral-300 focus:outline-none focus:ring-0 transition"
                                        placeholder="Titre du frais"
                                        aria-label="Titre du frais"
                                    />
                                ) : (
                                    <span className="font-bold text-2xl text-neutral-900 w-full mr-4">Nettoyage</span>
                                )}
                                <button
                                    onClick={onClose}
                                    className="w-7 h-7 flex items-center justify-center rounded-full border-2 border-black text-neutral-700 hover:bg-gray-100 transition ml-2 p-0"
                                    aria-label="Fermer"
                                    style={{ aspectRatio: '1 / 1' }}
                                >
                                    <X size={18} />
                                </button>
                            </div>
                            {titleEditable && feeTitleError && (
                                <div className="mt-1 flex items-center px-6" role="alert" aria-live="assertive">
                                    <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-2 shadow-sm w-full max-w-md">
                                        <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <circle cx="12" cy="12" r="10" strokeWidth="2" />
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                                        </svg>
                                        <span className="text-sm text-red-700 font-medium">{feeTitleError}</span>
                                    </div>
                                </div>
                            )}
                            <div className="px-6 text-neutral-500 text-base mb-6">
                                Choisissez le type de frais et le prix pour ce service.
                            </div>
                            {/* Modal Body */}
                            <div className="px-6 space-y-6">
                                {/* Type de frais */}
                                <div className="flex flex-col gap-2">
                                    <label className="font-semibold text-base text-neutral-900" htmlFor="feeType">Type de frais</label>
                                    <Select
                                        value={feeType || "none"}
                                        onValueChange={(val) => {
                                            if (val === "none") {
                                                handleChange("feeType", "");
                                            } else {
                                                handleChange("feeType", val);
                                            }
                                        }}
                                    >
                                        <SelectTrigger className={`w-full bg-transparent border border-[#E8EAED] rounded-[4px] h-12 ${errors.feeType ? "border-red-500" : ""}`} id="feeType">
                                            <SelectValue placeholder="Type de frais" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="none" className="custom-select-item">Type de frais</SelectItem>
                                            <SelectItem value="par personne" className="custom-select-item">par personne</SelectItem>
                                            <SelectItem value="par séjour" className="custom-select-item">par séjour</SelectItem>
                                            <SelectItem value="par nuit" className="custom-select-item">par nuit</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.feeType && <p className="text-sm text-red-500 mt-1">{errors.feeType}</p>}
                                </div>
                                {/* Prix */}
                                <div className="flex flex-col gap-2">
                                    <label className="font-semibold text-base text-neutral-900" htmlFor="feePrice">Prix</label>
                                    <div className="relative">
                                        <Input
                                            id="feePrice"
                                            type="number"
                                            className={`bg-transparent border border-[#E8EAED] rounded-[4px] h-12 pr-14 text-base ${errors.price ? "border-red-500" : ""}`}
                                            value={price || ""}
                                            onChange={(e) => handleChange("price", parseFloat(e.target.value) || 0)}
                                            placeholder="0"
                                        />
                                        <span className="absolute right-4 top-1/2 -translate-y-1/2 text-sm text-gray-500 pointer-events-none">TND</span>
                                    </div>
                                    {errors.price && <p className="text-sm text-red-500 mt-1">{errors.price}</p>}
                                </div>
                            </div>
                            {/* Modal Footer */}
                            <div className="flex items-center justify-between px-6 py-6 mt-2 border-t border-[#E8EAED]">
                                <button
                                    type="button"
                                    onClick={onClose}
                                    className="text-[#EA580F] font-semibold px-4 py-2 rounded-[4px] bg-transparent hover:bg-[#EA580F12] transition"
                                >
                                    Annuler
                                </button>
                                <button
                                    type="button"
                                    onClick={handleConfirm}
                                    className="bg-[#EA580F] text-white font-bold px-6 py-2 rounded-[4px] hover:bg-[#d65a0f] transition"
                                >
                                    Enregistrer
                                </button>
                            </div>
                            {/* Custom style for dropdown hover */}
                            <style jsx global>{`
                              .custom-select-item[data-highlighted] {
                                background: #EA580E26 !important;
                              }
                            `}</style>
                        </motion.div>
                    </div>
                </Dialog>
            )}
        </AnimatePresence>
    );
});

FeeModal.displayName = "FeeModal";

export default FeeModal;
