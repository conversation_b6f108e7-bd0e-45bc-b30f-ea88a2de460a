"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import CommonLayout from "./CommonLayout";
import Congratulations from "./Congratulations";
import AddLocation from "./add-location";
import PageAddListingIntro from "./intro";
import PageAddListingPresenteAnnonce from "./presente-annonce";
import PageAddCategories from "./add-categories";
import PageAddType from "./add-type";
import AddGuestsAndRules from "./add-guests-and-rules";
import AddEquipments from "./add-equipments";
import AddImages from "./add-images";
import AddTitleAndDescription from "./add-title-and-description";
import AddPrice from "./add-price";
import AddDiscount from "./add-discount";
import AddDates from "./add-dates";
import AddConditions from "./add-conditions";
import AddPayments from "./add-payments";
import PageAddListingMidAnnonce from "./mid-annonce";
import PageAddListingFinaliserAnnonce from "./finaliser-annonce";
import { ADD_LISTING_STEPS } from "./steps";

const Page = ({
  params,
}: {
  params: { stepIndex?: string[] };
}) => {
  const router = useRouter();

  // Always call hooks at the top level
  useEffect(() => {
    // Redirect to /add-listing/2 if no stepIndex or if stepIndex is 1
    if (!params.stepIndex || params.stepIndex.length === 0 || params.stepIndex[0] === '1') {
      router.replace('/add-listing/2');
    }
  }, [params.stepIndex, router]);

  const stepKey = params.stepIndex?.[0] || "intro";
  const step = ADD_LISTING_STEPS.find((s) => s.key === stepKey);

  if (!step) {
    return <div>404 - Step not found</div>;
  }

  const StepComponent = step.component;
  return (
      <StepComponent />
  );
};

export default Page;