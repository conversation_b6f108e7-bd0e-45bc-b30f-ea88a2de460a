import { Fragment, useCallback, useState, useEffect } from "react"
import { Popover, Transition } from "@headlessui/react"
import ButtonPrimary from "@/shared/ButtonPrimary"
import ButtonThird from "@/shared/ButtonThird"
import Checkbox from "@/shared/Checkbox"
import type React from "react"

export interface FilterOption {
    icons: string | undefined;
    id: string
    name: string
}

interface Filters {
    propertyTypes: FilterOption[]
}

interface SelectedFilters {
    propertyTypes: string[]
}

interface TypeOfPlaceFilterProps {
    filters: Filters
    selectedFilters: SelectedFilters
    applyFilters: (filterType: "propertyTypes", value: string[]) => void
    clearFilters: (filterType?: "propertyTypes") => void
}

const TypeOfPlaceFilter: React.FC<TypeOfPlaceFilterProps> = ({
                                                                 filters,
                                                                selectedFilters,
applyFilters,
                                                                 clearFilters,
                                                             }) => {
    const [tempFilters, setTempFilters] = useState<string[]>(selectedFilters.propertyTypes)

    useEffect(() => {
        setTempFilters(selectedFilters.propertyTypes)
    }, [selectedFilters.propertyTypes])

    const renderXClear = useCallback(
        () => (
            <span
                onClick={(e) => {
                    e.stopPropagation() // prevent popover toggle when clicking the X
                    clearFilters("propertyTypes")
                }}
                className="ml-3 flex h-4 w-4 cursor-pointer items-center justify-center rounded-full bg-booking-orange text-white"
            >
        <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-3 w-3"
            viewBox="0 0 20 20"
            fill="currentColor"
        >
          <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
          />
        </svg>
      </span>
        ),
        [clearFilters],
    )

    const handleTempFilterChange = (id: string, checked: boolean) => {
        setTempFilters((prev) =>
            checked ? [...prev, id] : prev.filter((item) => item !== id),
        )
    }

    const isAnyFilterSelected = selectedFilters.propertyTypes.length > 0

    return (
        <Popover className="relative">
            {({ open, close }) => (
                <>
                    <Popover.Button
                        className={`flex items-center justify-center rounded-full border border-neutral-300 px-3 py-2 text-sm hover:border-neutral-400 focus:outline-none dark:border-neutral-700 dark:hover:border-neutral-600
              ${open ? "!border-booking-orange" : ""}
              ${isAnyFilterSelected ? "bg-light-orange text-booking-orange !border-booking-orange" : ""}
            `}
                    >
            <span>
              Type de logement {isAnyFilterSelected ? `(${selectedFilters.propertyTypes.length})` : ""}
            </span>
                        {isAnyFilterSelected ? renderXClear() : <i className="las la-angle-down ml-2"></i>}
                    </Popover.Button>
                    <Transition
                        as={Fragment}
                        enter="transition ease-out duration-200"
                        enterFrom="opacity-0 translate-y-1"
                        enterTo="opacity-100 translate-y-0"
                        leave="transition ease-in duration-150"
                        leaveFrom="opacity-100 translate-y-0"
                        leaveTo="opacity-0 translate-y-1"
                    >
                        <Popover.Panel className="absolute left-0 z-10 mt-3 w-screen max-w-sm px-4 sm:px-0 lg:max-w-md">
                            <div className="overflow-hidden rounded-2xl border border-neutral-200 bg-white shadow-xl dark:border-neutral-700 dark:bg-neutral-900">
                                <div className="relative flex flex-col space-y-5 px-5 py-6">
                                    <h3 className="text-xl font-medium">Types de propriété</h3>
                                    {filters.propertyTypes.map((item) => (
                                        <div key={item.id}>
                                            <Checkbox
                                                name={item.name}
                                                label={item.name}
                                                checked={tempFilters.includes(item.id)}
                                                onChange={(checked) => handleTempFilterChange(item.id, checked)}
                                            />
                                        </div>
                                    ))}
                                </div>
                                <div className="flex items-center justify-between bg-neutral-50 p-5 dark:border-t dark:border-neutral-800 dark:bg-neutral-900">
                                    <ButtonThird
                                        onClick={() => {
                                            setTempFilters([])
                                            clearFilters("propertyTypes")
                                            close()
                                        }}
                                        sizeClass="px-4 py-2 sm:px-5"
                                    >
                                        Effacer
                                    </ButtonThird>
                                    <ButtonPrimary
                                        onClick={() => {
                                            applyFilters("propertyTypes", tempFilters)
                                            close()
                                        }}
                                        sizeClass="px-4 py-2 sm:px-5"
                                    >
                                        Appliquer
                                    </ButtonPrimary>
                                </div>
                            </div>
                        </Popover.Panel>
                    </Transition>
                </>
            )}
        </Popover>
    )
}

export default TypeOfPlaceFilter
