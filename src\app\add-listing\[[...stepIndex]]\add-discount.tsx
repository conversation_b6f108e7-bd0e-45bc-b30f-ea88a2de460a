"use client";

import React, { FC, useState, useEffect } from "react";
import CommonLayout from "./CommonLayout";
import { motion, AnimatePresence } from "framer-motion";
import { useFormContext } from "../FormContext";
import Input from "@/shared/Input";

import {
  ChevronUp,
  Edit3,
  Settings2,
} from "lucide-react";

interface DiscountCardProps {
  id: string;
  title: string;
  description: string;
  nightlyRate: number | undefined;
  currentDiscount: { mode: "percent" | "fixed"; value: number | null } | undefined;
  onDiscountChange: (discountId: string, discount: { mode: "percent" | "fixed"; value: number | null }) => void;
  discountType: "weekly" | "monthly" | "firstBooking";
  isExpanded: boolean;
  onToggleExpand: (cardId: string) => void;
  error?: string;
}

const cardItemVariants = {
  hidden: { y: 50, opacity: 0 },
  show: {
    y: 0,
    opacity: 1,
    transition: { type: "spring", stiffness: 100, damping: 12, duration: 0.5 }
  },
};

const calculationSectionVariants = {
  collapsed: { height: 0, opacity: 0, marginTop: 0, marginBottom: 0 },
  expanded: {
    height: "auto",
    opacity: 1,
    marginTop: "0.75rem", // Corresponds to space-y-3 or mb-3
    marginBottom: "0.75rem",
    transition: { duration: 0.3, ease: "easeInOut" }
  },
};

const DiscountCard: FC<DiscountCardProps> = ({
  id,
  title,
  description,
  nightlyRate,
  currentDiscount,
  onDiscountChange,
  discountType,
  isExpanded,
  onToggleExpand,
  error,
}) => {
  const [mode, setMode] = useState<"percent" | "fixed">(currentDiscount?.mode || "percent");
  const [inputValue, setInputValue] = useState<string>(currentDiscount?.value?.toString() || "");

  useEffect(() => {
    setMode(currentDiscount?.mode || "percent");
    setInputValue(currentDiscount?.value?.toString() || "");
  }, [currentDiscount]);

  const handleModeChange = (newMode: "percent" | "fixed") => {
    setMode(newMode);
    setInputValue("");
    onDiscountChange(id, { mode: newMode, value: null });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow digits and empty string
    if (/^[0-9]*$/.test(value)) {
      setInputValue(value);
      const numericValue = value === "" ? null : parseFloat(value);
      onDiscountChange(id, { mode, value: numericValue });
    }
  };

  // Prevent non-numeric key presses (e.g., '-', 'e', etc.)
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (["-", "e", "+", ",", "."].includes(e.key)) {
      e.preventDefault();
    }
  };

  const basePriceForCondition = () => {
    if (!nightlyRate) return 0;
    if (discountType === "weekly") return nightlyRate * 7;
    if (discountType === "monthly") return nightlyRate * 28;
    return nightlyRate;
  };

  function calculateFinalPrice() {
    const base = basePriceForCondition();
    const discount = currentDiscount?.value ?? 0;
    if (mode === "percent") {
      if (discount >= 0 && discount <= 100) {
        return Math.max(0, base * (1 - discount / 100));
      }
      return base;
    } else {
      // Fixed amount
      if (discount >= 0) {
        return Math.max(0, base - discount);
      }
      return base;
    }
  }

  const basePriceLabel = () => {
    if (discountType === "weekly") return "Prix pour 7 nuits :";
    if (discountType === "monthly") return "Prix pour 28 nuits :";
    return "Tarif de base :";
  };

  const finalPriceLabel = () => {
    if (discountType === "weekly") return "Prix final pour 7 nuits :";
    if (discountType === "monthly") return "Prix final pour 28 nuits :";
    return "Tarif réduit :";
  };

  const formattedPrice = (price: number) => {
    const roundedPrice = Math.round(price);
    return roundedPrice.toLocaleString("fr-FR", { minimumFractionDigits: 0, maximumFractionDigits: 0 });
  };

  const isDiscountActive = currentDiscount && currentDiscount.value !== null && currentDiscount.value > 0;

  return (
    <motion.div
      variants={cardItemVariants}
      className={`w-full p-6 rounded-[4px] border-2 transition-colors duration-300 flex flex-col ${isDiscountActive ? 'border-orange-500 bg-orange-50' : 'border-neutral-300 hover:border-orange-400'}`}
    >
      <div className="flex flex-col md:flex-row md:items-start gap-4 md:gap-6">
        {/* Left Side: Title and Description */}
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-neutral-800 mb-1">{title}</h3>
          <p className="text-sm text-neutral-600">{description}</p>
        </div>
        {/* Right Side: Collapsed/Expanded Content */}
        <div className="flex-shrink-0 w-full md:w-64 text-left md:text-right">
          {!isExpanded ? (
            <div className="flex flex-col items-stretch md:items-end space-y-3">
              {isDiscountActive ? (
                <>
                  <div className="text-left md:text-right">
                    <p className="text-sm font-semibold text-orange-600">
                      {mode === "percent"
                        ? `${currentDiscount.value}% de remise`
                        : `-${formattedPrice(currentDiscount.value ?? 0)} DT de remise`}
                    </p>
                    <p className="text-md text-neutral-700">Prix final : <span className="font-bold">{formattedPrice(calculateFinalPrice())} DT</span></p>
                  </div>
                  <button
                    className="!py-1.5 !px-4 text-sm border border-orange-500 text-orange-500 hover:bg-orange-50 rounded-[2px] flex items-center justify-center w-full md:w-auto"
                    onClick={() => onToggleExpand(id)}
                  >
                    <Edit3 size={14} className="mr-1.5" /> Modifier
                  </button>
                </>
              ) : (
                <button
                  className="!py-1.5 !px-4 text-sm bg-orange-500 text-white hover:bg-orange-600 rounded-[2px] flex items-center justify-center w-full md:w-auto"
                  onClick={() => onToggleExpand(id)}
                >
                  <Settings2 size={16} className="mr-1.5" /> Configurer la réduction
                </button>
              )}
            </div>
          ) : null}
          <AnimatePresence initial={false}>
            {isExpanded && (
              <motion.div
                key="calculation-block"
                variants={calculationSectionVariants}
                initial="collapsed"
                animate="expanded"
                exit="collapsed"
                className="overflow-hidden"
              >
                <div className="space-y-3">
                  {/* Base Price Section */}
                  <div className="text-left md:text-right">
                    <p className="text-xs text-neutral-500">{basePriceLabel()}</p>
                    <p className="text-md font-semibold text-neutral-700">{formattedPrice(basePriceForCondition())} DT</p>
                  </div>
                  {/* Discount Type Toggle */}
                  <div className="flex flex-col md:flex-row items-stretch md:items-center justify-end gap-2 mb-2">
                    <label className={`cursor-pointer px-2 py-1 rounded-[2px] border text-center ${mode === "percent" ? 'border-orange-500 bg-orange-50 text-orange-600 font-semibold' : 'border-neutral-300 text-neutral-600'}`}
                      onClick={() => handleModeChange("percent")}
                    >
                      <span className="block leading-tight">%</span>
                      <span className="block text-xs">(Pourcentage)</span>
                    </label>
                    <label className={`cursor-pointer px-2 py-1 rounded-[2px] border text-center ${mode === "fixed" ? 'border-orange-500 bg-orange-50 text-orange-600 font-semibold' : 'border-neutral-300 text-neutral-600'}`}
                      onClick={() => handleModeChange("fixed")}
                    >
                      <span className="block leading-tight">DT</span>
                      <span className="block text-xs">(Montant fixe)</span>
                    </label>
                  </div>
                  {/* Discount Input Section */}
                  {/*
                    The input value/placeholder (e.g., 0) will always be on the left,
                    and the suffix (% or DT) will always be on the right, on all devices,
                    due to absolute positioning and sufficient right padding.
                  */}
                  <div className="flex items-center justify-start md:justify-end gap-2">
                    <span className="text-xl font-medium text-neutral-600">-</span>
                    <div className="relative w-24">
                      <Input
                        type="number"
                        id={id + "-discountInput"}
                        value={inputValue}
                        onChange={handleInputChange}
                        onKeyDown={handleKeyDown}
                        inputMode="numeric"
                        pattern="[0-9]*"
                        className="w-full h-10 !pr-12 text-right rounded-[2px] border-neutral-300 focus:border-orange-500 focus:ring-orange-500"
                        placeholder="0"
                        min="0"
                      />
                      <span className="absolute right-4  top-1/2 -translate-y-1/2 text-neutral-500 text-md">{mode === "percent" ? "%" : "DT"}</span>
                    </div>
                    {/* Error Feedback */}
                    {error && (
                      <div className="mt-1 flex items-center justify-center" role="alert" aria-live="assertive">
                        <div className="flex items-center bg-red-50 border border-red-200 rounded px-4 py-3 shadow-sm w-full max-w-md">
                          <svg className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <circle cx="12" cy="12" r="10" strokeWidth="2" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01" />
                          </svg>
                          <span className="text-sm text-red-700 font-medium">{error}</span>
                        </div>
                      </div>
                    )}
                  </div>
                  {/* Separator */}
                  <hr className="border-neutral-200" />
                  {/* Final Price Section */}
                  <div className="flex items-baseline justify-start md:justify-end gap-2">
                    <span className="text-xl font-bold text-neutral-900">=</span>
                    <p className="text-xl font-bold text-neutral-900 text-left md:text-right">{formattedPrice(calculateFinalPrice())} DT</p>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
          {isExpanded && (
            <div className="mt-4 flex justify-start md:justify-end">
              <button
                className="!py-1.5 !px-4 text-sm border border-orange-500 text-orange-500 hover:bg-orange-50 rounded-[2px] flex items-center justify-center w-full md:w-auto"
                onClick={() => onToggleExpand(id)}
              >
                <ChevronUp size={16} className="mr-1.5" /> Masquer les détails
              </button>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

interface DiscountTypeInfo {
  id: string;
  title: string;
  description: string;
  discountTypeKey: "weeklyDiscount" | "monthlyDiscount" | "firstBookingDiscount";
  type: "weekly" | "monthly" | "firstBooking";
}

const discountTypesData: DiscountTypeInfo[] = [
  {
    id: "weekly",
    title: "Réduction Hebdomadaire",
    description: "Offrez une réduction pour les séjours de 7 nuits ou plus.",
    discountTypeKey: "weeklyDiscount",
    type: "weekly",
  },
  {
    id: "monthly",
    title: "Réduction Mensuelle",
    description: "Proposez un tarif avantageux pour les séjours de 28 nuits ou plus.",
    discountTypeKey: "monthlyDiscount",
    type: "monthly",
  },
  {
    id: "firstBooking",
    title: "Offre Nouveaux Voyageurs",
    description: "Remise spéciale pour la première réservation d'un voyageur.",
    discountTypeKey: "firstBookingDiscount",
    type: "firstBooking",
  },
];

const listContainerVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

const AddDiscount: FC = () => {
  const { formData, setFormData, formErrors, validateStep } = useFormContext();
  const [expandedCardId, setExpandedCardId] = useState<string | null>(null);

  useEffect(() => {
    if (
      formData.weeklyDiscount?.value != null ||
      formData.monthlyDiscount?.value != null ||
      formData.firstBookingDiscount?.value != null
    ) {
      validateStep("add-discount");
    }
  }, [
    formData.weeklyDiscount?.value,
    formData.monthlyDiscount?.value,
    formData.firstBookingDiscount?.value,
    validateStep,
  ]);

  const handleToggleExpand = (cardId: string) => {
    setExpandedCardId(prevId => (prevId === cardId ? null : cardId));
  };

  const handleDiscountChange = (discountId: string, discount: { mode: "percent" | "fixed"; value: number | null }) => {
    const discountInfo = discountTypesData.find(d => d.id === discountId);
    if (discountInfo) {
      setFormData(prev => {
        // Only persist if value is a positive number
        if (discount.value && discount.value > 0) {
          return {
            ...prev,
            [discountInfo.discountTypeKey]: discount,
          };
        } else {
          // Remove the discount object if value is null, undefined, or 0
          const { [discountInfo.discountTypeKey]: _removed, ...rest } = prev;
          return rest;
        }
      });
    }
  };

  return (
    <CommonLayout params={{ stepIndex: "add-discount" }}>
      <div className="max-w-3xl mx-auto 2xl:pt-20 px-4 py-16">
        <div className="mb-12 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-3 leading-tight">
            Gérez vos réductions
          </h2>
          <p className="text-base text-neutral-500">
            Augmentez vos réservations en proposant des offres attractives à vos futurs voyageurs.
          </p>
        </div>
        <motion.div
          variants={listContainerVariants}
          initial="hidden"
          animate="show"
          className="space-y-6"
        >
          {discountTypesData.map((discount) => (
            <DiscountCard
              key={discount.id}
              id={discount.id}
              title={discount.title}
              description={discount.description}
              nightlyRate={formData.nightlyRate ?? undefined}
              currentDiscount={formData[discount.discountTypeKey] as { mode: "percent" | "fixed"; value: number | null } | undefined}
              onDiscountChange={handleDiscountChange}
              discountType={discount.type}
              isExpanded={expandedCardId === discount.id}
              onToggleExpand={handleToggleExpand}
              error={formErrors[discount.discountTypeKey]}
            />
          ))}
        </motion.div>
      </div>
    </CommonLayout>
  );
};

export default AddDiscount; 