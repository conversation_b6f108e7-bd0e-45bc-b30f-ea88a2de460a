"use client";

import React, { useRef, useEffect } from "react";
import AddListingHeader from "./AddListingHeader";
import BottomNavigation from "./BottomNavigation";
import TopProgressBar from "./TopProgressBar";

interface MainLayoutProps {
    children: React.ReactNode;
    fullWidth?: boolean;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children, fullWidth = false }) => {
    const footerRef = useRef<HTMLDivElement>(null);
    const mainRef = useRef<HTMLElement>(null);

    useEffect(() => {
        function setFooterHeightVar() {
            if (footerRef.current && mainRef.current) {
                const height = footerRef.current.offsetHeight;
                mainRef.current.style.setProperty('--footer-height', height + 'px');
            }
        }
        setFooterHeightVar();
        window.addEventListener('resize', setFooterHeightVar);
        return () => window.removeEventListener('resize', setFooterHeightVar);
    }, []);

    return (
        <div className="min-h-screen flex flex-col bg-[#FCFBFF]">
            {/* Header */}
            <AddListingHeader />

            {/* Main Content */}
            <main
                ref={mainRef}
                className={`flex-1 bg-[#FCFBFF]${fullWidth ? '' : ' container pb-[92px] md:pb-8'}`}
                style={{
                    ...(fullWidth ? { height: 'calc(100vh - 92px)', minHeight: 0 } : {}),
                    paddingBottom: 'var(--footer-height, 92px)'
                }}
            >
                {children}
            </main>

            {/* Footer */}
            <div className="bottom-sticky" ref={footerRef}>
                <TopProgressBar />
                <div className="bg-white border-t border-gray-200 z-30 shadow-top">
                    <BottomNavigation />
                </div>
            </div>
        </div>
    );
};

export default MainLayout;