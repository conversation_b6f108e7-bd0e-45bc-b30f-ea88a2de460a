'use client'

import React, {FC, useEffect, useState} from "react";
import imagePng from "@/images/hero-right.png";
import HeroSearchForm from "../(client-components)/(HeroSearchForm)/HeroSearchForm";
import Image from "next/image";
import ButtonPrimary from "@/shared/ButtonPrimary";
import { useSearch } from "../(stay-listings)/SearchContext";
import {useRouter} from "next/navigation";
import HeroSearchFormMobile from "../(client-components)/(HeroSearchFormMobile)/HeroSearchFormMobile";

export interface SectionHeroProps {
  className?: string;
}

const SectionHero: FC<SectionHeroProps> = ({ className = "" }) => {
  const router = useRouter()
  const { searchParams, setSearchParams } = useSearch()
  const [heroSearchParams, setHeroSearchParams] = useState(searchParams)

  const handleSearchParamsChange = ( heroSearchParams: any ) => {
    setHeroSearchParams(heroSearchParams);
  };

  const handleSubmit = () => {
    setSearchParams(heroSearchParams)
    router.push("/listing-stay-map")
  }

  return (
      <div
          className={`nc-SectionHero flex flex-col-reverse lg:flex-col relative ${className}`}
      >
        <div className="flex flex-col lg:flex-row lg:items-center">
          <div className="flex-shrink-0 lg:w-1/2 flex flex-col items-start space-y-8 sm:space-y-10 md:pb-14 lg:pb-64 xl:pr-14 lg:mr-10 xl:mr-0">
            <div className="flex flex-col">
            <p className="mt-4 text-lg text-neutral-500">Votre vision, notre précision — ensemble avec Almindhar</p>
              <h2 className="font-semibold text-[3rem] md:text-[4rem] leading-tight flex flex-col">
                <span>Explorez.</span>
                <span className="ml-12">Planifiez.</span>
                <span className="relative ml-24">
                  <div className="absolute bottom-0 top-[35px] md:top-[45px] z-0 w-auto" style={{ width: 'calc(100% - 6px)', left: '3px' }}>
                    <Image 
                      src="https://api.almindharbooking.com/storage/v1/object/public/uploads//Group%20(2).svg"
                      alt="Orange underline"
                      width={200}
                      height={20}
                      className="w-[80%] h-auto "
                    />
                  </div>
                  <span className="relative z-10 inline-block">Réservez.</span>
                </span>
              </h2>
          
            </div>

            {/* Mobile search filter */}
            <div className="block lg:hidden w-full">
              <HeroSearchFormMobile />
            </div>
            {/* Desktop search button remains for spacing/alignment, can be removed if not needed */}
            {/* <ButtonPrimary onClick={handleSubmit} sizeClass="px-5 py-4 sm:px-7">
              Commencez votre recherche
            </ButtonPrimary> */}
          </div>
          <div className="flex-grow hidden md:block">
            <Image className="w-full" src={imagePng} alt="hero" priority/>
          </div>
        </div>

        <div className="hidden lg:block z-10 mb-12 lg:mb-0 lg:-mt-64 w-full">
          <HeroSearchForm onSearchChange={handleSearchParamsChange}/>
        </div>
      </div>
  );
};

export default SectionHero;
