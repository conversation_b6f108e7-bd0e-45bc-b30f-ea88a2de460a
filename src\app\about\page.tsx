import Image from "next/image"
import Link from "next/link"
import { HistoryTimeline } from "./About_Components/history-timeline"
import { StatisticsSection } from   "./About_Components/statistics-setion"
import { NewsletterSubscription } from "./About_Components/newsletter-subscription"

export default function Home() {
  return (
    <main className="min-h-screen bg-white">
      {/* About Section */}
      <section className="w-full py-24">
        <div className="text-center px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-6 relative inline-block text-[#16214f]">
            À propos d&apos;Almindhar Booking
            <div className="absolute -bottom-2 right-0 w-full">
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Screenshot%202025-03-28%20103357-3o0mj7zjtnLdrW3nommm3BfbXWCiDv.png"
                alt="Stylized underline"
                width={400}
                height={20}
                className="w-full h-auto"
              />
            </div>
          </h1>
          <p className="mt-8 text-center mx-auto text-[#1a1a1a] max-w-3xl">
            Bienvenue sur Almindhar Booking, la plateforme qui simplifie la réservation de maisons de vacances.
            <br />
            Que vous soyez propriétaire ou voyageur, nous rendons chaque expérience fluide, sécurisée et agréable.
          </p>

          <div className="flex flex-wrap justify-center gap-4 mt-6">
            <Link
              href="/help-center"
              className="bg-[#ea580e] text-white px-6 py-3 rounded-md font-medium hover:bg-opacity-90 transition-colors"
            >
              How to get started
            </Link>
            <Link
              href="/"
              className="bg-white text-[#1a1a1a] px-6 py-3 rounded-md font-medium border border-[#d9d9d9] hover:bg-[#f5f5f5] transition-colors"
            >
              Check out our solution
            </Link>
          </div>

          <div className="mt-8 w-full">
            <Image
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-2oF1UCEkSMaBNZIhHGVDnnzQwxgd28.png"
              alt="Travelers looking at vacation properties with modern houses and city skyline"
              width={1920}
              height={600}
              className="w-full h-auto"
              priority
            />
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="w-full py-32">
        <div className="text-center px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-[#16214f]">La mission d&apos;Almindhar Booking</h2>
          <p className="mt-4 text-center mx-auto text-[#1a1a1a] max-w-3xl">
            Notre objectif ? Réunir les hôtes et les voyageurs sur une seule plateforme intuitive et fiable.
            <br />
            Nous offrons aux propriétaires une visibilité optimale et aux voyageurs un large choix d&apos;hébergements,
            toujours au meilleur prix.
          </p>
        </div>

        <div className="mt-12 w-full">
          <Image
            src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Artboard%201-1%201.jpg-yKNUkmwfjF2agKFpROWUZc8cLvqqdY.jpeg"
            alt="Person using a tablet with vacation rental analytics"
            width={1920}
            height={800}
            className="w-full h-auto"
          />
        </div>
      </section>

      {/* History Section */}
      <section className="w-full py-32 text-center relative">
        <h2 className="text-3xl md:text-4xl font-bold mb-16 text-[#16214f]">L&apos;histoire d&apos;Almindhar Booking</h2>
        <HistoryTimeline />
      </section>

      {/* Statistics Section */}
      <section className="w-full py-32 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-12 text-[#16214f]">Almindhar Booking en quelques chiffres</h2>
        <div className="bg-white py-4 px-4">
          <StatisticsSection />
        </div>
      </section>

      {/* Newsletter Subscription */}
      <div className="w-full pb-32 px-4">
        <NewsletterSubscription />
      </div>
    </main>
  )
}

