"use client"

import type React from "react"
import { createContext, useContext, useState, type ReactNode } from "react"
import type { GuestsObject } from "@/app/(client-components)/(HeroSearchFormSmall)/GuestsInput"

interface SearchContextType {
    searchParams: SearchParams
    setSearchParams: React.Dispatch<React.SetStateAction<SearchParams>>
    mobileTabFiltersVisible: boolean;
    setMobileTabFiltersVisible: (visible: boolean) => void;
}

export interface SearchParams {
    location: string
    checkIn: Date | null
    checkOut: Date | null
    guests: GuestsObject
    propertyTypes: string[]
    propertyCategories: string[]
    amenities: string[]
    priceRange: [number, number]
    beds: number
    bedrooms: number
    bathrooms: number
    kitchens: number
    mapBounds: google.maps.LatLngBounds | null
    paymentType: string | null
    roomTypes: string[]
    minRating: number
}

const initialSearchParams: SearchParams = {
    location: "",
    checkIn: null,
    checkOut: null,
    guests: { guestAdults: 0, guestChildren: 0, guestInfants: 0 },
    propertyTypes: [],
    propertyCategories: [],
    amenities: [],
    priceRange: [0, 1000],
    beds: 0,
    bedrooms: 0,
    bathrooms: 0,
    kitchens: 0,
    mapBounds: null,
    paymentType: null,
    roomTypes: [],
    minRating: 0
}

const SearchContext = createContext<SearchContextType>({
    searchParams: initialSearchParams,
    setSearchParams: () => {},
    mobileTabFiltersVisible: false,
    setMobileTabFiltersVisible: () => {},
});

export const SearchProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [searchParams, setSearchParams] = useState<SearchParams>(initialSearchParams);
    const [mobileTabFiltersVisible, setMobileTabFiltersVisible] = useState(false);

    return <SearchContext.Provider
        value={{ searchParams, setSearchParams, mobileTabFiltersVisible, setMobileTabFiltersVisible }}
    >
        {children}
    </SearchContext.Provider>
}

export const useSearch = () => {
    const context = useContext(SearchContext)
    if (context === undefined) {
        throw new Error("useSearch must be used within a SearchProvider")
    }
    return context
}