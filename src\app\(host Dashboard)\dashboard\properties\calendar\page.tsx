import { Suspense } from "react"
import { CalendarView } from "./calendar-view"
import { getBookings } from "@/app/actions/get-bookings"

export default async function CalendarPage() {
    const bookings = await getBookings()

    return (
        <div className="p-6 space-y-6">
            <Suspense fallback={<div>Loading calendar...</div>}>
                <CalendarView initialBookings={bookings} />
            </Suspense>
        </div>
    )
}

