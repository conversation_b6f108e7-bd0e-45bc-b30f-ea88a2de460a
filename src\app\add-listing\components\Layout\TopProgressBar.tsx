"use client";

import React from "react";
import { useParams } from "next/navigation";
import { ADD_LISTING_STEPS } from '../../[[...stepIndex]]/steps';

export const TopProgressBar: React.FC = () => {
    const params = useParams();
    // Find the current step index from the step key in the URL
    const stepKey = Array.isArray(params.stepIndex) ? params.stepIndex[0] : params.stepIndex;
    const currentStepIdx = ADD_LISTING_STEPS.findIndex(s => s.key === stepKey);
    const totalSteps = ADD_LISTING_STEPS.length;
    const numSegments = 3;

    // Calculate smooth progress (0 at first step, 1 at last step)
    const progress = currentStepIdx === -1 ? 0 : Math.max(0, Math.min(1, currentStepIdx / (totalSteps - 1)));

    // For each segment, calculate fill percentage based on overall progress
    const segmentFill = Array.from({ length: numSegments }).map((_, i) => {
        const segStart = i / numSegments;
        const segEnd = (i + 1) / numSegments;
        if (progress >= segEnd) return 1; // fully filled
        if (progress <= segStart) return 0; // not filled
        // partially filled
        return (progress - segStart) * numSegments;
    });

    // Render the progress bar with 3 segments, each filling smoothly as the user advances
    return (
        <div className="w-full mb-[0.05rem]">
            <div>
                <div className="flex w-full gap-2">
                    {segmentFill.map((fill, idx) => (
                        <div
                            key={idx}
                            className="flex-1 h-2 rounded-[2px] bg-gray-200 overflow-hidden relative"
                        >
                            <div
                                className="absolute left-0 top-0 h-full bg-orange-500 transition-all duration-500 rounded-[2px]"
                                style={{
                                    width: `${fill * 100}%`,
                                    borderRadius: '2px',
                                    transitionProperty: 'width',
                                }}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default TopProgressBar;