import { Fragment, useCallback, useState, useEffect } from "react"
import { Popover, Transition } from "@headlessui/react"
import ButtonPrimary from "@/shared/ButtonPrimary"
import ButtonThird from "@/shared/ButtonThird"
import Slider from "rc-slider"
import convertNumbThousand from "@/utils/convertNumbThousand"
import type React from "react" // Added import for React
import { useSearch } from "../SearchContext"

interface SelectedFilters {
    propertyTypes: string[]
    propertyCategories: string[]
    amenities: string[]
    priceRange: [number, number]
}

interface PriceRangeFilterProps {
    priceRange: [number, number]
    setPriceRange: (value: [number, number]) => void
    clearFilters: (filterType?: "price") => void
    applyFilters: (filters: Partial<SelectedFilters>) => void
}

const PriceRangeFilter: React.FC<PriceRangeFilterProps> = ({
                                                               priceRange,
                                                               setPriceRange,
                                                               clearFilters,
                                                               applyFilters,
                                                           }) => {
    const [tempPriceRange, setTempPriceRange] = useState<[number, number]>(priceRange)
    const { searchParams, setSearchParams } = useSearch()

    useEffect(() => {
        setTempPriceRange(priceRange)
    }, [priceRange])

    const renderXClear = useCallback(
        () => (
            <span
                onClick={(e) => {
                    e.stopPropagation() // prevent popover toggle when clicking the X
                    clearFilters("price")
                }}
                className="ml-3 flex h-4 w-4 cursor-pointer items-center justify-center rounded-full bg-booking-orange text-white">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
          <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
          />
        </svg>
      </span>
        ),
        [],
    )

    const [min, max] = searchParams.priceRange;
    const isAnyFilterSelected = min !== 0 || max !== 1000;

    return (
        <Popover className="relative">
            {({ open, close }) => (
                <>
                    <Popover.Button
                        className={`flex items-center justify-center rounded-full border border-neutral-300 px-3 py-2 text-sm hover:border-neutral-400 focus:outline-none dark:border-neutral-700 dark:hover:border-neutral-600
                          ${open ? "!border-booking-orange" : ""}
                          ${isAnyFilterSelected ? "bg-light-orange text-booking-orange !border-booking-orange" : ""}
                        `}
                    >
                        <span>{`${convertNumbThousand(priceRange[0])} DT - ${convertNumbThousand(priceRange[1])} DT`}</span>
                        {isAnyFilterSelected ? renderXClear() : <i className="las la-angle-down ml-2"></i>}
                    </Popover.Button>
                    <Transition
                        as={Fragment}
                        enter="transition ease-out duration-200"
                        enterFrom="opacity-0 translate-y-1"
                        enterTo="opacity-100 translate-y-0"
                        leave="transition ease-in duration-150"
                        leaveFrom="opacity-100 translate-y-0"
                        leaveTo="opacity-0 translate-y-1"
                    >
                        <Popover.Panel className="absolute left-0 z-10 mt-3 w-screen max-w-sm px-4 sm:px-0">
                            <div className="overflow-hidden rounded-2xl border border-neutral-200 bg-white shadow-xl dark:border-neutral-700 dark:bg-neutral-900">
                                <div className="relative flex flex-col space-y-8 px-5 py-6">
                                    <div className="space-y-5">
                                        <span className="font-medium">Prix par jour</span>
                                        <Slider
                                            range
                                            className="text-booking-orange "
                                            min={0}
                                            max={1000}
                                            defaultValue={tempPriceRange}
                                            allowCross={false}
                                            onChange={(e) => setTempPriceRange(e as [number, number])}
                                        />
                                    </div>

                                    <div className="flex justify-between space-x-5">
                                        <div>
                                            <label
                                                htmlFor="minPrice"
                                                className="block text-sm font-medium text-neutral-700 dark:text-neutral-300"
                                            >
                                                Prix minimum
                                            </label>
                                            <div className="relative mt-1 rounded-md">
                                                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                                    <span className="text-neutral-500 sm:text-sm">DT</span>
                                                </div>
                                                <input
                                                    type="number"
                                                    name="minPrice"
                                                    id="minPrice"
                                                    className="block w-full rounded-full border-neutral-200 pr-7 pl-3 text-neutral-900 focus:border-booking-orange focus:ring-booking-orange sm:text-sm"
                                                    value={tempPriceRange[0]}
                                                    onChange={(e) => setTempPriceRange([Number(e.target.value), tempPriceRange[1]])}
                                                />
                                            </div>
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="maxPrice"
                                                className="block text-sm font-medium text-neutral-700 dark:text-neutral-300"
                                            >
                                                Prix maximum
                                            </label>
                                            <div className="relative mt-1 rounded-md">
                                                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                                    <span className="text-neutral-500 sm:text-sm">DT</span>
                                                </div>
                                                <input
                                                    type="number"
                                                    name="maxPrice"
                                                    id="maxPrice"
                                                    className="block w-full rounded-full border-neutral-200 pr-7 pl-3 text-neutral-900 focus:border-booking-orange focus:ring-booking-orange sm:text-sm"
                                                    value={tempPriceRange[1]}
                                                    onChange={(e) => setTempPriceRange([tempPriceRange[0], Number(e.target.value)])}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="flex items-center justify-between bg-neutral-50 p-5 dark:border-t dark:border-neutral-800 dark:bg-neutral-900">
                                    <ButtonThird
                                        onClick={() => {
                                            clearFilters("price")
                                            close()
                                        }}
                                        sizeClass="px-4 py-2 sm:px-5"
                                    >
                                        Effacer
                                    </ButtonThird>
                                    <ButtonPrimary
                                        onClick={() => {
                                            applyFilters({ priceRange: tempPriceRange })
                                            close()
                                        }}
                                        sizeClass="px-4 py-2 sm:px-5"
                                    >
                                        Appliquer
                                    </ButtonPrimary>
                                </div>
                            </div>
                        </Popover.Panel>
                    </Transition>
                </>
            )}
        </Popover>
    )
}

export default PriceRangeFilter

