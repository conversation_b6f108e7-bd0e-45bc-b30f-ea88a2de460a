"use server"

import { createClient } from "@/utils/supabase/server"

// Define the additional fee type
type AdditionalFee = {
    price: number;
    title: string;
    feeType: string;
    isOptional: boolean;
}

export type Booking = {
    id: string
    user_id: string
    listing_id: string
    start_date: string
    end_date: string
    num_guests: number
    status: "demande" | "pending" | "confirmed" | "canceled" | "refunded" | "active"
    payment_status?: "pending" | "paid" | "failed" | "refunded"
    payment_ref?: string
    total_price: number
    created_at: string
    updated_at: string
    listing: {
        listing_photos?: {
            photo_url: string
        }[]
        id: string
        title: string
        description: string
        address: string
        payment_type: "Instant" | "Cashbased"
        featured_image_url: string | null
        listing_pricing: {
            nightly_rate: number
            additional_fees?: AdditionalFee[]
        }
    }
    user: {
        id: string
        fullname: string
        phone_number: string
        email:string
        avatar_url: string | null
    }
}

export async function getBookings() {
    const supabase =await createClient()

    // First, get the current user's ID (the host)
    const {
        data: { user },
        error: userError,
    } = await supabase.auth.getUser()
    if (userError) throw new Error("Failed to get user")

    // Fetch bookings for listings owned by the current user (host)
    const { data: bookings, error: bookingsError } = await supabase
        .from("bookings")
        .select(`
      *,
      listing:listings (
        id,
        title,
        description,
        address,
        type_id,
        payment_type,
        featured_image_url,
        listing_pricing (
          nightly_rate,
          additional_fees
        )
          
      )
    `)
        .eq("listings.host_id", user?.id)
        .order("created_at", { ascending: false })

    if (bookingsError) {
        console.error("Error fetching bookings:", bookingsError)
        throw new Error("Failed to fetch bookings")
    }

    // Filter out bookings with null listings
    const validBookings = bookings.filter((booking) => booking.listing !== null)

    // If no valid bookings found, return empty array
    if (validBookings.length === 0) {
        return []
    }

    // Fetch user details separately
    const userIds = validBookings.map((booking) => booking.user_id)
    const { data: users, error: usersError } = await supabase.from("profiles").select("id, fullname, phone_number,avatar_url").in("id", userIds)

    if (usersError) {
        console.error("Error fetching users:", usersError)
        throw new Error("Failed to fetch user details")
    }

    // Combine booking data with user data
    const bookingsWithUserDetails = validBookings.map((booking) => ({
        ...booking,
        user: users.find((user) => user.id === booking.user_id) || {
            id: booking.user_id,
        },
    }))

    return bookingsWithUserDetails as Booking[]
}

