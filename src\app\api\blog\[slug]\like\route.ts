import { NextResponse } from "next/server"
import { createClient } from "@/utils/supabase/server"

export async function POST(request: Request, { params }: { params: { slug: string } }) {
    const supabase = await createClient()
    const slugOrId = params.slug

    try {
        // Parse the request body
        const { liked, userId } = await request.json()

        if (!userId) {
            return NextResponse.json({ success: false, error: "User ID is required" }, { status: 400 })
        }

        let blogId: string | number = slugOrId

        // Check if the slug is actually a numeric ID
        if (!isNaN(Number(slugOrId))) {
            // It's a numeric ID, use it directly
            blogId = Number(slugOrId)
        } else {
            // It's a slug, look up the ID
            const { data: blogPost, error: blogError } = await supabase
                .from("BlogPosts")
                .select("id")
                .eq("slug", slugOrId)
                .single()

            if (blogError || !blogPost) {
                return NextResponse.json({ success: false, error: "Blog post not found" }, { status: 404 })
            }

            blogId = blogPost.id
        }

        if (liked) {
            // First check if the like already exists
            const { data: existingLike } = await supabase
                .from("BlogLikes")
                .select("id")
                .eq("blog_post_id", blogId)
                .eq("user_id", userId)
                .single()

            // Only insert if it doesn't exist
            if (!existingLike) {
                await supabase.from("BlogLikes").insert({
                    blog_post_id: blogId,
                    user_id: userId,
                })
            }
        } else {
            // Remove the like
            await supabase.from("BlogLikes").delete().eq("blog_post_id", blogId).eq("user_id", userId)
        }

        // Get updated like count
        const { count, error: countError } = await supabase
            .from("BlogLikes")
            .select("*", { count: "exact", head: true })
            .eq("blog_post_id", blogId)

        if (countError) {
            throw countError
        }

        return NextResponse.json(
            {
                success: true,
                likes: count || 0,
                action: liked ? "liked" : "unliked",
            },
            {
                headers: {
                    "Cache-Control": "no-store",
                },
            },
        )
    } catch (error) {
        console.error("Error processing like:", error)
        return NextResponse.json({ success: false, error: "Failed to process like" }, { status: 500 })
    }
}
