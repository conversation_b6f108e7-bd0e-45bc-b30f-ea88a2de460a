# Add Listing Flow Plan (Robust Architecture)

This document outlines the robust, maintainable, and extensible architecture for the add-listing process. It covers step structure, data flow, validation, navigation, draft recovery, and extensibility. Update as needed to reflect changes in the flow or requirements.

---

## 1. Step Registry

All steps are defined in a central registry, each with its order, name, and validation schema. This enables easy reordering, addition, or removal of steps.

Example:
```ts
const stepRegistry = [
  { step: 1, name: "add-categorie", schema: stepSchemas[1] },
  { step: 2, name: "add-type", schema: stepSchemas[2] },
  { step: 3, name: "add-location", schema: stepSchemas[3] },
  { step: 4, name: "add-guests-and-rules", schema: stepSchemas[4] },
  { step: 5, name: "add-equipments", schema: stepSchemas[5] },
  { step: 6, name: "add-images", schema: stepSchemas[6] },
  { step: 7, name: "add-title-and-description", schema: stepSchemas[7] },
  { step: 8, name: "add-conditions", schema: stepSchemas[8] },
  { step: 9, name: "add-price", schema: stepSchemas[9] },
  { step: 10, name: "add-discount", schema: stepSchemas[10] },
  { step: 11, name: "add-dates", schema: stepSchemas[11] },
  { step: 12, name: "add-payments", schema: stepSchemas[12] },
  { step: 13, name: "final-review", schema: stepSchemas[13] },
  { step: 14, name: "publish", schema: stepSchemas[14] },
];
```

---

## 2. Data Flow & State Management

- **FormContext** is the single source of truth for all listing data, errors, step status, and visited steps.
- **LocalStorage** is used for persistence and recovery. All changes to form data are synced to localStorage.
- On mount, FormContext restores from localStorage if available.

---

## 3. Validation

- **Per-Step Validation:** Each step uses its schema (Yup by default) to validate data before allowing navigation.
- **Global Validation:** On the final step (publish), all steps are validated. If any step is invalid, navigation is blocked and errors are shown.
- **Field-Level Validation:** For instant feedback, fields can be validated on blur/change as needed.

---

## 4. Navigation & User Flow

- **Step Navigation:**
  - User can only proceed to the next step if the current step is valid.
  - Visited steps are tracked for progress indicators and draft recovery.
- **TabNavigation** and **CommonLayout** handle navigation, draft recovery, and exit warnings.

---

## 5. Draft Recovery

- On mount, if a draft exists in localStorage, the user is prompted to restore or discard.
- If restored, the form loads from localStorage; if discarded, localStorage is cleared.

---

## 6. Final Submission

- On the last step (publish), all data is validated.
- If valid, data is submitted to the backend.
- On success, localStorage is cleared and a confirmation is shown.

---

## 7. Extensibility & Maintenance

- **To add a new step:**
  - Add to the step registry.
  - Create a validation schema for the step.
  - Update FormContext and UI as needed.
- **To reorder steps:**
  - Update the step registry order.
- **To update validation:**
  - Edit the relevant schema in `formSchemas`.
- **Documentation:**
  - Keep this plan up to date with step structure, data model, and validation rules.

---

*This plan is a living document. Update as the flow evolves or requirements change.* 