import { NextResponse } from "next/server"
import { createClient } from "@/utils/supabase/server"

export async function GET(request: Request, { params }: { params: { slug: string } }) {
    const supabase = await createClient()
    const slugOrId = params.slug

    try {
        let blogId: string | number = slugOrId

        // Check if the slug is actually a numeric ID
        if (!isNaN(Number(slugOrId))) {
            // It's a numeric ID, use it directly
            blogId = Number(slugOrId)
        } else {
            // It's a slug, look up the ID
            const { data: blogPost, error: blogError } = await supabase
                .from("BlogPosts")
                .select("id")
                .eq("slug", slugOrId)
                .single()

            if (blogError || !blogPost) {
                console.error("Error fetching blog post:", blogError)
                return NextResponse.json(
                    { likes: 0 },
                    {
                        status: 200,
                        headers: {
                            "Cache-Control": "no-store",
                        },
                    },
                )
            }

            blogId = blogPost.id
        }

        // Count total likes for this post
        const { count, error } = await supabase
            .from("BlogLikes")
            .select("*", { count: "exact", head: true })
            .eq("blog_post_id", blogId)

        if (error) {
            console.error("Error counting likes:", error)
            return NextResponse.json(
                { likes: 0 },
                {
                    status: 200,
                    headers: {
                        "Cache-Control": "no-store",
                    },
                },
            )
        }

        // Return the like count with no-store cache control
        return NextResponse.json(
            { likes: count || 0 },
            {
                headers: {
                    "Cache-Control": "no-store",
                },
            },
        )
    } catch (error) {
        console.error("Error fetching likes:", error)
        return NextResponse.json(
            { likes: 0 },
            {
                status: 200,
                headers: {
                    "Cache-Control": "no-store",
                },
            },
        )
    }
}
