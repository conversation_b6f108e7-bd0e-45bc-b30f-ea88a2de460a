"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { <PERSON>, Settings, MapPin, Eye, EyeOff } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { ListingData } from "@/data/types"
import { useRouter } from "next/navigation"
import { formatAddress } from "@/app/(listing-detail)/listing-stay-detail/helpers"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { toast } from "@/hooks/use-toast"

export default function ListingManagementPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [sortOrder, setSortOrder] = useState<"newest" | "oldest">("newest")
  const [listings, setListings] = useState<ListingData[]>([])
  const [filteredListings, setFilteredListings] = useState<ListingData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [listingToDelete, setListingToDelete] = useState<string | null>(null)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const [isUpdating, setIsUpdating] = useState(false)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      // Only handle click outside if there's an active dropdown
      if (activeDropdown) {
        const target = event.target as HTMLElement;
        
        // Check if the click is on a settings button or inside a dropdown
        const isSettingsButton = target.closest('[data-settings-button]');
        const isInsideDropdown = target.closest('[data-dropdown-menu]');
        
        // If it's neither on a settings button nor inside a dropdown, close the active dropdown
        if (!isSettingsButton && !isInsideDropdown) {
          setActiveDropdown(null);
        }
      }
    }
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [activeDropdown]);

  // Fetch listings from the API
  useEffect(() => {
    const fetchListings = async () => {
      try {
        setIsLoading(true)
        const response = await fetch("/api/host-listings-details")
        if (!response.ok) {
          throw new Error("Échec de la récupération des annonces")
        }
        const data = await response.json()
        if (data.success) {
          console.log("Listings:", data.listings)
          // The API already returns listings sorted by newest first
          setListings(data.listings)
          setFilteredListings(data.listings)
        } else {
          throw new Error(data.error || "Échec de la récupération des annonces")
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Une erreur s'est produite")
        console.error("Erreur lors de la récupération des annonces:", err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchListings()
  }, [])

  // Filter listings based on search query
  useEffect(() => {
    const lowercasedQuery = searchQuery.toLowerCase()
    const filtered = listings.filter(
      (listing) =>
        listing.propertyTitle?.toLowerCase().includes(lowercasedQuery) ||
        listing.propertyCity?.toLowerCase().includes(lowercasedQuery),
    )

    // Sort the filtered listings
    const sorted = [...filtered];
    
    // Sort by newest/oldest
    // Since the API already returns listings in newest first order,
    // we can use the array order as an approximation for creation date
    if (sortOrder === "newest") {
      // Keep original order (API returns newest first)
      // No need to sort again
    } else if (sortOrder === "oldest") {
      // Reverse the array to get oldest first
      sorted.reverse();
    }

    setFilteredListings(sorted)
  }, [searchQuery, listings, sortOrder])

  const getStatusBadge = (status?: string) => {
    if (status === "active") {
      return (
        <div className="bg-white rounded-full px-4 py-2 inline-flex items-center">
          <span className="font-medium text-sm">Publiée</span>
        </div>
      )
    } else if (status === "pending" || status === "draft") {
      return (
        <div className="bg-white rounded-full px-4 py-2 inline-flex items-center">
          <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
          <span className="font-medium text-sm">En attente</span>
        </div>
      )
    } else if (status === "hidden") {
      return (
        <div className="bg-white rounded-full px-4 py-2 inline-flex items-center">
          <div className="w-2 h-2 bg-gray-500 rounded-full mr-2"></div>
          <span className="font-medium text-sm">Masquée</span>
        </div>
      )
    }
    return null
  }

  // Toggle dropdown
  const toggleDropdown = (propertyId: string, event: React.MouseEvent) => {
    // Prevent the event from propagating to document click listener
    event.stopPropagation();
    
    console.log("toggleDropdown", propertyId);
    
    if (activeDropdown === propertyId) {
      setActiveDropdown(null);
    } else {
      setActiveDropdown(propertyId);
    }
  }

  // Handle toggling listing visibility
  const handleToggleVisibility = (propertyId: string, currentStatus: string, event: React.MouseEvent) => {
    // Prevent the event from bubbling up and closing the dropdown prematurely
    event.stopPropagation();
    
    // Only allow toggling between active and hidden
    if (currentStatus !== 'active' && currentStatus !== 'hidden') {
      toast({
        title: "Action non autorisée",
        description: "Vous ne pouvez pas modifier le statut de cette annonce",
        variant: "destructive",
      });
      return;
    }
    
    const newStatus = currentStatus === 'active' ? 'hidden' : 'active';
    console.log(`Toggling listing ${propertyId} from ${currentStatus} to ${newStatus}`);
    
    // Call the function to update the listing status
    updateListingStatus(propertyId, newStatus);
    
    // Close the dropdown after action
    setActiveDropdown(null);
  }

  // Update listing status in the database
  const updateListingStatus = async (propertyId: string, newStatus: string) => {
    if (!propertyId) return;
    
    try {
      setIsUpdating(true);
      
      // Find the listing to get the hostId
      const listing = listings.find(listing => listing.propertyId === propertyId);
      
      if (!listing || !listing.hostId) {
        throw new Error("Détails de l'annonce introuvables");
      }
      
      // Make the API call to update the listing status
      const response = await fetch(`/api/listing-ahmed/${propertyId}+${listing.hostId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: newStatus
        }),
      });
      
      if (!response.ok) {
        throw new Error("Échec de la mise à jour de l'annonce");
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Update the listing status in the local state
        setListings(prevListings => prevListings.map(item => 
          item.propertyId === propertyId 
            ? { ...item, status: newStatus } 
            : item
        ));
        
        toast({
          title: newStatus === 'hidden' ? "Annonce masquée" : "Annonce publiée",
          description: newStatus === 'hidden' 
            ? "L'annonce a été masquée avec succès" 
            : "L'annonce a été publiée avec succès",
          variant: "default",
        });
      } else {
        throw new Error(result.error || "Échec de la mise à jour de l'annonce");
      }
    } catch (err) {
      console.error("Erreur lors de la mise à jour de l'annonce:", err);
      toast({
        title: "Erreur",
        description: err instanceof Error ? err.message : "Une erreur s'est produite",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  }

  // Handle delete button click
  const handleDeleteClick = (propertyId: string, event: React.MouseEvent) => {
    // Prevent the event from bubbling up
    event.stopPropagation();
    
    console.log("handleDeleteClick", propertyId);
    
    setListingToDelete(propertyId);
    setIsDeleteDialogOpen(true);
    
    // Close the dropdown after action
    setActiveDropdown(null);
  }

  // Handle actual deletion
  const handleDeleteListing = async () => {
    if (!listingToDelete) return

    try {
      const listing = listings.find(listing => listing.propertyId === listingToDelete);
      const response = await fetch(`/api/listing-ahmed/${listingToDelete}+${listing?.hostId}`, {        
        method: "DELETE",
      })

      if (!response.ok) {
        throw new Error("Échec de la suppression de l'annonce")
      }

      const result = await response.json()

      if (result.success) {
        // Remove the listing from state
        setListings((prevListings) => prevListings.filter((listing) => listing.propertyId !== listingToDelete))
        toast({
          title: "Annonce supprimée",
          description: "L'annonce a été supprimée avec succès",
          variant: "default",
        })
      } else {
        throw new Error(result.error || "Échec de la suppression de l'annonce")
      }
    } catch (err) {
      console.error("Erreur lors de la suppression de l'annonce:", err)
      toast({
        title: "Erreur",
        description: err instanceof Error ? err.message : "Une erreur s'est produite",
        variant: "destructive",
      })
    } finally {
      setIsDeleteDialogOpen(false)
      setListingToDelete(null)
    }
  }

  // Get property type and beds text
  const getPropertyTypeAndBeds = (listing: ListingData) => {
    const propertyType = listing.propertyType || "Propriété";
    // As bedCount doesn't exist on ListingData, use a fallback
    const beds = "1";
    
    return `${propertyType} · ${beds} beds`;
  }

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="text-center">Chargement des annonces...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="text-center text-red-500">Erreur : {error}</div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Rechercher par titre ou ville"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <Select value={sortOrder} onValueChange={(value) => setSortOrder(value as "newest" | "oldest")}>
          <SelectTrigger className="w-[120px]">
            <SelectValue>{sortOrder === "newest" ? "Plus récent" : "Plus ancien"}</SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="newest">Plus récent</SelectItem>
            <SelectItem value="oldest">Plus ancien</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {filteredListings.length === 0 ? (
        <div className="text-center py-10 text-gray-500">Aucune annonce trouvée</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredListings.map((listing) => (
            <div 
              key={listing.propertyId} 
              className="bg-gray-100 rounded-xl overflow-hidden relative"
            >
              <div className="relative">
                <div className="absolute top-3 left-3 z-10">
                  {getStatusBadge(listing.status)}
                </div>
                <div className="absolute top-3 right-3 z-10">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="bg-white rounded-full h-10 w-10 flex items-center justify-center"
                    onClick={(e) => toggleDropdown(listing.propertyId || "", e)}
                    data-settings-button={listing.propertyId}
                    disabled={isUpdating}
                  >
                    <Settings className="h-5 w-5 text-gray-700" />
                  </Button>
                  
                  {activeDropdown === listing.propertyId && (
                    <div 
                      className="absolute right-0 mt-2 w-44 bg-white rounded-xl shadow-lg overflow-hidden z-20"
                      data-dropdown-menu={listing.propertyId}
                    >
                      <div className="py-1 divide-y divide-gray-100">
                        {(listing.status === 'active' || listing.status === 'hidden') && (
                          <button 
                            className="block w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 items-center"
                            onClick={(e) => handleToggleVisibility(listing.propertyId || "", listing.status || "", e)}
                            disabled={isUpdating}
                          >
                            {listing.status === 'active' ? (
                              <>
                                <EyeOff className="mr-2 h-4 w-4" />
                                Masquer
                              </>
                            ) : (
                              <>
                                <Eye className="mr-2 h-4 w-4" />
                                Publier
                              </>
                            )}
                          </button>
                        )}
                        <button 
                          className="block w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50"
                          onClick={(e) => handleDeleteClick(listing.propertyId || "", e)}
                          disabled={isUpdating}
                        >
                          Supprimer
                        </button>
                      </div>
                    </div>
                  )}
                </div>
                <div className="w-full h-52">
                  <Image
                    src={listing.coverImageUrl || "/placeholder.svg"}
                    alt={listing.propertyTitle || ""}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    className="object-cover"
                  />
                </div>
              </div>
              
              <div className="p-4 space-y-2">
                <div className="text-sm text-gray-500">
                  {getPropertyTypeAndBeds(listing)}
                </div>
                
                <h3 className="font-semibold text-lg">{listing.propertyTitle}</h3>
                
                <div className="flex items-center text-gray-500">
                  <MapPin className="h-4 w-4 mr-1" />
                  <span className="text-sm">{listing.propertyCity || formatAddress(listing.propertyAddress)}</span>
                </div>
                
                <div className="flex items-center justify-between pt-4">
                  <div className="flex items-baseline">
                    <span className="font-medium text-lg">{listing.nightlyRate} TND</span>
                    <span className="text-sm text-gray-500 ml-1">/nuit</span>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button 
                      variant="outline"
                      size="sm"
                      onClick={() => router.push(`/dashboard/properties/listings/${listing.propertyId}/edit`)}
                      className="px-4"
                      disabled={isUpdating}
                    >
                      Modifier
                    </Button>
                    <Button 
                      variant="default"
                      size="sm"
                      onClick={() => router.push(`/dashboard/properties/listings/${listing.propertyId}/details`)}
                      className="bg-[#E45A29] hover:bg-[#E45A29]/90 text-white px-4"
                      disabled={isUpdating}
                    >
                      Voir
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Êtes-vous sûr de vouloir supprimer cette annonce?</AlertDialogTitle>
            <AlertDialogDescription>
              Cette action ne peut pas être annulée. Cela supprimera définitivement votre annonce et toutes les données
              associées.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Annuler</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteListing} className="bg-red-500 hover:bg-red-600">
              Supprimer
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}