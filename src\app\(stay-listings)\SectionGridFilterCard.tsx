"use client"

import { type FC, useState, useEffect, useRef } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import Heading2 from "@/shared/Heading2"
import StayCard2 from "@/components/StayCard2"
import type { ListingData } from "@/data/types"
import Spinner from "@/components/Spinner"
import { ChevronLeft, ChevronRight, MapPin, Search, Home, X } from "lucide-react"
import NewStayCard from "@/components/NewStayCard";
import {mapListingToStayData} from "@/utils/mapListingToStayData";

export interface SectionGridFilterCardProps {
  className?: string
}

const transformApiDataToListingData = (apiData: any): ListingData => {
  const cityState = `${apiData.city || ""}, ${apiData.state || ""}`.trim()
  const formattedLocation = cityState !== "," ? cityState : apiData.address

  return {
    propertyId: apiData.id,
    propertyTitle: apiData.title?.trim(),
    propertyAddress: apiData.address,
    propertyType: apiData.type,
    propertyTypeId: apiData.listingCategory?.href?.split("/").pop() || "",
    numGuests: apiData.maxGuests,
    numBedrooms: apiData.bedrooms,
    paymentType: apiData.paymentType,
    numBeds: apiData.beds,
    numBathrooms: apiData.bathrooms,
    coverImageUrl: apiData.featuredImage,
    placeImagesUrl: apiData.galleryImgs,
    nightlyRate: apiData.price?.split(" ")[0],
    reviewCount: apiData.reviewCount,
    averageRating: apiData.reviewStart,
    hostId: apiData.authorId,
    propertyState: apiData.state || "",
    propertyCity: apiData.city || "",
    location: {
      city: apiData.city || "",
      state: apiData.state || "",
      formatted: formattedLocation,
    },
  }
}

const SectionGridFilterCard: FC<SectionGridFilterCardProps> = ({ className = "" }) => {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [data, setData] = useState<ListingData[]>([])
  const [filteredData, setFilteredData] = useState<ListingData[]>([])
  const [cities, setCities] = useState<string[]>([])
  const [citiesLoading, setCitiesLoading] = useState(false)
  const [selectedCity, setSelectedCity] = useState<string>("")
  const [searchTerm, setSearchTerm] = useState<string>("")
  const [filteredCities, setFilteredCities] = useState<string[]>([])
  const searchParams = useSearchParams()
  const router = useRouter()
  const stateId = searchParams.get("state")
  const cityParam = searchParams.get("city")
  const [state, setState] = useState<string | null>(null)
  const [stateName, setStateName] = useState<string | null>(null)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [cityListingMap, setCityListingMap] = useState<Record<string, ListingData[]>>({})
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(false)

  const scrollLeft = () => {
    scrollContainerRef.current?.scrollBy({ left: -200, behavior: "smooth" })
  }

  const scrollRight = () => {
    scrollContainerRef.current?.scrollBy({ left: 200, behavior: "smooth" })
  }

  const checkScrollButtonsVisibility = () => {
    const container = scrollContainerRef.current
    if (!container) return
    setCanScrollLeft(container.scrollLeft > 0)
    setCanScrollRight(container.scrollLeft + container.clientWidth < container.scrollWidth)
  }

  useEffect(() => {
    const container = scrollContainerRef.current
    if (!container) return

    checkScrollButtonsVisibility()

    const handleScroll = () => checkScrollButtonsVisibility()
    const handleResize = () => checkScrollButtonsVisibility()

    container.addEventListener("scroll", handleScroll)
    window.addEventListener("resize", handleResize)

    return () => {
      container.removeEventListener("scroll", handleScroll)
      window.removeEventListener("resize", handleResize)
    }
  }, [filteredCities])

  useEffect(() => {
    const fetchListings = async () => {
      if (!stateId) {
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      try {
        const response = await fetch(`/api/listing_stay?state_id=${stateId}`)
        const result = await response.json()

        if (!response.ok || !result.success) {
          throw new Error(result.error || `API request failed`)
        }

        const transformedData = result.listings.map(transformApiDataToListingData)
        setData(transformedData)

        const cityToListingsMap: Record<string, ListingData[]> = {}
        transformedData.forEach((listing: ListingData) => {
          const city = listing.propertyCity
          if (city) {
            cityToListingsMap[city] = cityToListingsMap[city] || []
            cityToListingsMap[city].push(listing)
          }
        })

        setCityListingMap(cityToListingsMap)

        const citiesWithListings = Object.keys(cityToListingsMap)
          .filter((c) => cityToListingsMap[c].length > 0)
          .sort()

        setCities(citiesWithListings)
        setFilteredCities(citiesWithListings)

        if (transformedData[0]?.propertyState) {
          setState(transformedData[0].propertyState)
        } else if (result.listings[0]?.state) {
          setState(result.listings[0].state)
        }

        setStateName(result.listings[0]?.property_name || "")

        if (selectedCity && cityToListingsMap[selectedCity]) {
          setFilteredData(cityToListingsMap[selectedCity])
        } else {
          setFilteredData(transformedData)
        }

        setError(null)
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load listings")
      } finally {
        setIsLoading(false)
      }
    }

    fetchListings()
  }, [stateId, selectedCity])

  useEffect(() => {
    if (cities.length > 0 && cityParam) {
      if (cities.includes(cityParam)) {
        setSelectedCity(cityParam)
      } else {
        const params = new URLSearchParams(searchParams.toString())
        params.delete("city")
        router.push(`?${params.toString()}`, { scroll: false })
      }
    }
  }, [cities, cityParam, router, searchParams])

  useEffect(() => {
    setFilteredCities(
      searchTerm.trim() === ""
        ? cities
        : cities.filter((city) => city.toLowerCase().includes(searchTerm.toLowerCase()))
    )
  }, [searchTerm, cities])

  useEffect(() => {
    const timeout = setTimeout(() => {
      checkScrollButtonsVisibility()
    }, 100)
    return () => clearTimeout(timeout)
  }, [filteredCities])

  const handleCityChange = (city: string) => {
    setSelectedCity(city)
    setSearchTerm("")
    setFilteredData(city ? cityListingMap[city] ?? [] : data)

    const params = new URLSearchParams(searchParams.toString())
    if (city) {
      params.set("city", city)
    } else {
      params.delete("city")
    }

    router.push(`?${params.toString()}`, { scroll: false })
  }

  const clearSearch = () => setSearchTerm("")

  const displayData = filteredData.length > 0 ? filteredData : data

  return (
    <div className={`nc-SectionGridFilterCard ${className}`} data-nc-id="SectionGridFilterCard">
      <Heading2 location={stateName ?? undefined} numListings={displayData.length} />

      <div className="mb-8 lg:mb-11 " >
        <div className="px-0">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              {/* <MapPin className="w-5 h-5 text-primary mr-2" />
              <h3 className="text-lg font-medium">Explore {stateName} Cities</h3> */}
            </div>

            <div className="relative w-full max-w-xs">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-10 py-2 border border-gray-200 rounded-lg focus:ring-primary focus:border-primary text-sm"
                placeholder="Rechercher une ville..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button onClick={clearSearch} className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                </button>
              )}
            </div>
          </div>

          {selectedCity && (
            <div className="mb-4 flex items-center">
                <button
                  onClick={() => handleCityChange("")}
                  className="ml-2 p-1 hover:bg-primary/20 rounded-full"
                  aria-label="Clear selection"
                >
                  {/* <X className="w-3 h-3" /> */}
                </button>
              
            </div>
          )}

<div className="relative">
  {canScrollLeft && (
    <button
      onClick={scrollLeft}
      className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full shadow-md p-2 hover:bg-gray-100 transition-colors"
      aria-label="Scroll left"
    >
      <ChevronLeft className="w-5 h-5" />
    </button>
  )}

  <div
    ref={scrollContainerRef}
    className="overflow-x-auto scrollbar-hide scroll-smooth"
    style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
  >
    <div className="flex items-center gap-3 pr-4 py-2 min-w-max">
      {/* All Cities Button */}
      <div onClick={() => handleCityChange("")}>
        <div className="px-4 py-2 rounded-full text-sm font-semibold bg-neutral-900 text-white flex items-center gap-x-2 cursor-pointer">
          <span>Toutes les villes</span>
          {!selectedCity && <span>({data.length})</span>}
        </div>
      </div>

      {/* Dynamic City Buttons */}
      {filteredCities.length > 0 ? (
        filteredCities.map((city) => {
          const cityCount = cityListingMap[city]?.length || 0
          if (cityCount > 0) {
            const isSelected = selectedCity === city
            return (
              <div
                key={city}
                onClick={() => handleCityChange(city)}
                className={`px-4 py-2 rounded-full text-sm font-medium flex items-center gap-x-2 cursor-pointer ${
                  isSelected
                    ? "bg-neutral-900 text-white"
                    : "text-neutral-500 hover:text-neutral-800 hover:bg-gray-100"
                }`}
              >
                <span>{city}</span>
             
                <span className="text-xs bg-gray-800 text-white rounded-full px-2 py-0.5">
                  {cityCount}
                </span>
              </div>
            )
          }
          return null
        })
      ) : searchTerm ? (
        <div className="px-6 py-3 rounded-lg bg-gray-50 flex items-center justify-center min-w-[200px] border border-gray-200">
          <span className="text-gray-500">Aucune ville trouvée</span>
        </div>
      ) : (
        <div className="px-6 py-3 rounded-lg bg-gray-50 flex items-center justify-center min-w-[200px] border border-gray-200">
          <span className="text-gray-500">Aucune ville avec des séjours disponibles</span>
        </div>
      )}
    </div>
  </div>

  {canScrollRight && (
    <button
      onClick={scrollRight}
      className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full shadow-md p-2 hover:bg-gray-100 transition-colors"
      aria-label="Scroll right"
    >
      <ChevronRight className="w-5 h-5" />
    </button>
  )}
</div>

        </div>
      </div>

      {isLoading && (
        <div className="flex justify-center items-center py-20">
          <Spinner />
        </div>
      )}

      {error && (
        <div className="text-center text-red-500 py-5 bg-red-50 rounded-lg">
          <p className="font-medium">Erreur lors du chargement des séjours</p>
          <p className="text-sm">{error}</p>
        </div>
      )}

      {!isLoading && !error && (
        <div className="grid grid-cols-1 gap-6 md:gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {displayData.length > 0 ? (
            displayData.map((listing) => <NewStayCard key={listing.propertyId} data={mapListingToStayData(listing)} />)
          ) : (
            <div className="col-span-full text-center py-10 bg-gray-50 rounded-lg">
              <p className="text-lg font-medium">Aucun séjour trouvé</p>
              <p className="text-gray-500 mt-2">
                {selectedCity
                  ? `There are no properties available in ${selectedCity}`
                  : "No properties match your criteria"}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default SectionGridFilterCard