import { Fragment, useCallback, useEffect, useState } from "react"
import { Dialog, Transition } from "@headlessui/react"
import ButtonPrimary from "@/shared/ButtonPrimary"
import ButtonThird from "@/shared/ButtonThird"
import ButtonClose from "@/shared/ButtonClose"
import Checkbox from "@/shared/Checkbox"
import { StarIcon } from "@heroicons/react/24/solid"

export interface FilterOption {
    id: string
    name: string
}

interface SelectedFilters {
    amenities: string[]
    roomTypes: string[]
    minRating?: number
}

interface MoreFiltersModalProps {
    isOpen: boolean
    onClose: () => void
    filters: {
        amenities: FilterOption[]
        roomTypes: FilterOption[]
    }
    selectedFilters: SelectedFilters
    applyFilters: (filters: SelectedFilters) => void
    clearFilters: () => void
}

const MoreFiltersModal: React.FC<MoreFiltersModalProps> = ({
                                                               isOpen,
                                                               onClose,
                                                               filters,
                                                               selectedFilters,
                                                               applyFilters,
                                                               clearFilters,
                                                           }) => {
    const [localFilters, setLocalFilters] = useState<SelectedFilters>(selectedFilters)

    useEffect(() => {
        if (isOpen) {
            setLocalFilters({ ...selectedFilters })
        }
    }, [isOpen, selectedFilters])

    type ArrayFilterKey = "amenities" | "roomTypes"
    const handleLocalFilterChange = useCallback(
        (filterType: ArrayFilterKey, id: string, checked: boolean) => {
            setLocalFilters((prev) => ({
                ...prev,
                [filterType]: checked
                    ? [...prev[filterType], id]
                    : prev[filterType].filter((item) => item !== id),
            }))
        },
        []
    )

    const handleClearFilters = useCallback(() => {
        const clearedFilters = { amenities: [], roomTypes: [], minRating: undefined }
        setLocalFilters(clearedFilters)
        clearFilters()
        onClose()
    }, [clearFilters, onClose])

    const handleApplyFilters = useCallback(() => {
        applyFilters({ ...localFilters })
        onClose()
    }, [localFilters, applyFilters, onClose])

    const renderMoreFilterItem = useCallback(
        (filterType: ArrayFilterKey, data: FilterOption[]) => {
            if (!data || data.length === 0) {
                return null
            }
            const list1 = data.filter((_, i) => i < data.length / 2)
            const list2 = data.filter((_, i) => i >= data.length / 2)
            return (
                <div className="grid grid-cols-2 gap-8">
                    <div className="flex flex-col space-y-5">
                        {list1.map((item) => (
                            <Checkbox
                                key={item.id}
                                name={item.name}
                                label={item.name}
                                checked={localFilters[filterType].includes(item.id)}
                                onChange={(checked) => handleLocalFilterChange(filterType, item.id, checked)}
                            />
                        ))}
                    </div>
                    <div className="flex flex-col space-y-5">
                        {list2.map((item) => (
                            <Checkbox
                                key={item.id}
                                name={item.name}
                                label={item.name}
                                checked={localFilters[filterType].includes(item.id)}
                                onChange={(checked) => handleLocalFilterChange(filterType, item.id, checked)}
                            />
                        ))}
                    </div>
                </div>
            )
        },
        [localFilters, handleLocalFilterChange]
    )

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="fixed inset-0 z-50 overflow-y-auto" onClose={onClose}>
                <div className="min-h-screen text-center">
                    <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <div className="fixed inset-0 bg-black bg-opacity-40 dark:bg-opacity-60" />
                    </Transition.Child>

                    <span className="inline-block h-screen align-middle" aria-hidden="true">
                        &#8203;
                    </span>
                    <Transition.Child
                        as={"div"}
                        className="inline-block h-screen w-full max-w-4xl px-2 py-8"
                        enter="ease-out duration-300"
                        enterFrom="opacity-0 scale-95"
                        enterTo="opacity-100 scale-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100 scale-100"
                        leaveTo="opacity-0 scale-95"
                    >
                        <div className="inline-flex h-full w-full max-w-4xl transform flex-col overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all dark:border dark:border-neutral-700 dark:bg-neutral-900 dark:text-neutral-100">
                            <div className="relative flex-shrink-0 border-b border-neutral-200 px-6 py-4 text-center dark:border-neutral-800">
                                <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                                    Plus de filtres
                                </Dialog.Title>
                                <span className="absolute left-3 top-3">
                                    <ButtonClose onClick={onClose} />
                                </span>
                            </div>

                            <div className="flex-grow overflow-y-auto">
                                <div className="divide-y divide-neutral-200 px-10 dark:divide-neutral-800">
                                    {/* ——— Minimum Rating Filter ——— */}
                                    <div className="py-7">
                                        <h3 className="text-xl font-medium">Note minimale</h3>
                                        <div className="mt-6 flex items-center justify-center space-x-6">
                                            {[5, 4, 3, 2, 1].map((r) => (
                                                <label
                                                    key={r}
                                                    className="flex items-center space-x-1 cursor-pointer"
                                                >
                                                    <input
                                                        type="radio"
                                                        name="minRating"
                                                        value={r}
                                                        checked={localFilters.minRating === r}
                                                        onChange={() =>
                                                            setLocalFilters((prev) => ({
                                                                ...prev,
                                                                minRating: prev.minRating === r ? undefined : r, // Toggle behavior
                                                            }))
                                                        }
                                                        className="sr-only"
                                                    />
                                                    <div
                                                        className={`flex items-center justify-center gap-2 rounded-full border px-4 py-3 text-lg focus:outline-none dark:border-neutral-700 dark:hover:border-neutral-600 ${
                                                            localFilters.minRating === r
                                                                ? "bg-light-orange border-booking-orange"
                                                                : "border-neutral-300 hover:border-neutral-400"
                                                        }`}
                                                    >
                                                        <StarIcon className="w-[28px] h-[28px] text-orange-500" /> {r}
                                                    </div>
                                                </label>
                                            ))}
                                            {/* No "Aucune" option needed, the toggle works */}
                                        </div>
                                    </div>

                                    <div className="py-7">
                                        <h3 className="text-xl font-medium">Type de chambre</h3>
                                        <div className="relative mt-6">{renderMoreFilterItem("roomTypes", filters.roomTypes)}</div>
                                    </div>

                                    <div className="py-7">
                                        <h3 className="text-xl font-medium">Équipements</h3>
                                        <div className="relative mt-6">{renderMoreFilterItem("amenities", filters.amenities)}</div>
                                    </div>
                                </div>
                            </div>

                            <div className="flex flex-shrink-0 items-center justify-between bg-neutral-50 p-6 dark:border-t dark:border-neutral-800 dark:bg-neutral-900">
                                <ButtonThird onClick={handleClearFilters} sizeClass="px-4 py-2 sm:px-5">
                                    Effacer
                                </ButtonThird>
                                <ButtonPrimary onClick={handleApplyFilters} sizeClass="px-4 py-2 sm:px-5">
                                    Appliquer
                                </ButtonPrimary>
                            </div>
                        </div>
                    </Transition.Child>
                </div>
            </Dialog>
        </Transition>
    )
}

export default MoreFiltersModal
