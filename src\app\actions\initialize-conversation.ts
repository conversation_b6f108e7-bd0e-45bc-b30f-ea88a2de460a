"use server"

import { createClient } from "@/utils/supabase/server"
import {sendMessage} from "@/app/api/messages/actions";

type InitializeConversationParams = {
    hostId: string
    listingId: string
    bookingId?: string
    checkIn?: Date
    checkOut?: Date
    guests?: number
    initialMessage?: string
}

/**
 * Initializes or retrieves a conversation between a guest and host for a specific booking.
 * Each booking gets its own dedicated conversation thread, even if the same guest and host
 * have other conversations for different bookings.
 */
export async function initializeConversation(params: InitializeConversationParams) {
    try {
        const supabase = await createClient()
        const { data: { user }, error: userError } = await supabase.auth.getUser()

        if (userError || !user) {
            return { success: false, error: "auth_required" }
        }

        // Check for existing conversations with this host, regardless of listing
        const { data, error } = await supabase
            .from('conversation_participants')
            .select('conversation_id, user_id')
            .or(`user_id.eq.${user.id},user_id.eq.${params.hostId}`)

        if (error) throw error

        // Group by conversation_id and find those with both users
        const conversationCounts = data.reduce((acc: Record<string, string[]>, item) => {
            if (!acc[item.conversation_id]) {
                acc[item.conversation_id] = []
            }
            acc[item.conversation_id].push(item.user_id)
            return acc
        }, {})

        // Find conversations that have both users
        const sharedConversationIds = Object.entries(conversationCounts)
            .filter(([_, userIds]) => 
                userIds.includes(user.id) && userIds.includes(params.hostId)
            )
            .map(([convId, _]) => convId)

        let existingConversation = null
        if (sharedConversationIds.length > 0) {
            // Get the most recent conversation
            const { data: conversations, error: convError } = await supabase
                .from('conversations')
                .select('*')
                .in('id', sharedConversationIds)
                .order('updated_at', { ascending: false })
                .limit(1)
                
            if (!convError && conversations && conversations.length > 0) {
                existingConversation = conversations[0]
                
                // Update the conversation with the new booking ID if provided
                if (params.bookingId && !existingConversation.booking_id) {
                    await supabase
                        .from('conversations')
                        .update({ booking_id: params.bookingId })
                        .eq('id', existingConversation.id)
                }
            }
        }
        
        // If we found an existing conversation, use it
        if (existingConversation) {
            // If there's an initial message, send it to the existing conversation
            if (params.initialMessage) {
                await sendMessage(existingConversation.id, params.initialMessage, user.id);
                
                // Update the conversation's updated_at timestamp
                await supabase
                    .from('conversations')
                    .update({ updated_at: new Date().toISOString() })
                    .eq('id', existingConversation.id);
            }
            
            return {
                success: true,
                data: existingConversation,
                existing: true
            }
        }

        // If no existing conversation, create a new one
        const { data: conversation, error: newConversationError } = await supabase
            .from('conversations')
            .insert({
                booking_id: params.bookingId,
                listing_id: params.listingId,
                host_id: params.hostId
            })
            .select()
            .single();

        if (newConversationError) throw newConversationError;

        // Add participants
        const { error: participantError } = await supabase
            .from('conversation_participants')
            .insert([
                { conversation_id: conversation.id, user_id: user.id },
                { conversation_id: conversation.id, user_id: params.hostId }
            ]);

        if (participantError) throw participantError;

        // If there's an initial message, create it
        if (params.initialMessage) {
            await sendMessage(conversation.id, params.initialMessage, user.id);
        }
        
        return {
            success: true,
            data: conversation,
            existing: false
        }
    } catch (error: any) {
        console.error("Error initializing conversation:", error);
        return { success: false, error: error.message }
    }
}
