"use client";

import { ClockIcon, MapPinIcon } from "@heroicons/react/24/outline";
import React, { useState, useRef, useEffect, FC } from "react";
import ClearDataButton from "./ClearDataButton";
import {useSearch} from "@/app/(stay-listings)/SearchContext";
import useOutsideAlerter from "@/hooks/useOutsideAlerter";

export interface LocationInputProps {
  placeHolder?: string
  desc?: string
  className?: string
  divHideVerticalLineClass?: string
  autoFocus?: boolean
  defaultValue?: string
  onChange?: (location: string) => void
}

const LocationInput: FC<LocationInputProps> = ({
                                                autoFocus = false,
                                                placeHolder = "Destination",
                                                desc = "Où voulez-vous aller?",
                                                className = "nc-flex-1.5",
                                                divHideVerticalLineClass = "left-10 -right-0.5",
                                                 defaultValue = "",
                                                 onChange,
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const { searchParams } = useSearch()

  const [value, setValue] = useState(defaultValue || searchParams.location || "")
  const [showPopover, setShowPopover] = useState(autoFocus)
  const [suggestions, setSuggestions] = useState<google.maps.places.AutocompletePrediction[]>([])
  const [recentSearches, setRecentSearches] = useState<string[]>([
    "Tunis, Tunisia",
    "Sousse, Tunisia",
    "Sfax, Tunisia",
    "Hammamet, Tunisia",
  ])

  useEffect(() => {
    setShowPopover(autoFocus)
    if (autoFocus && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus()
      }, 200)
    }
  }, [autoFocus])

  useOutsideAlerter(containerRef, () => {
    setShowPopover(false)
  })

  useEffect(() => {
    if (showPopover && inputRef.current) {
      inputRef.current.focus()
    }
  }, [showPopover])

  useEffect(() => {
    if (value && window.google && window.google.maps) {
      const service = new window.google.maps.places.AutocompleteService()
      const sessionToken = new window.google.maps.places.AutocompleteSessionToken()
      service.getPlacePredictions(
          {
            input: value,
            sessionToken: sessionToken,
            componentRestrictions: { country: "tn" }, // Restrict to Tunisia
            types: ["geocode"], // Restrict to addresses
          },
          (predictions, status) => {
            if (status === window.google.maps.places.PlacesServiceStatus.OK && predictions) {
              setSuggestions(predictions)
            } else {
              setSuggestions([])
            }
          },
      )
    } else {
      setSuggestions([])
    }
  }, [value])

  const handleSelectLocation = (item: google.maps.places.AutocompletePrediction | string) => {
    const selectedValue = typeof item === "string" ? item : item.description
    setValue(selectedValue)
    onChange && onChange(selectedValue)
    setShowPopover(false)
    setRecentSearches((prev) => [selectedValue, ...prev.filter((i) => i !== selectedValue)].slice(0, 4))
  }

  const renderRecentSearches = () => {
    return (
      <>
        <h3 className="block mt-2 sm:mt-0 px-4 sm:px-8 font-semibold text-base sm:text-lg text-neutral-800 dark:text-neutral-100">
          Recent searches
        </h3>
        <div className="mt-2">
          {recentSearches.map((item) => (
              <span
                onClick={() => handleSelectLocation(item)}
                key={item}
                className="flex px-4 sm:px-8 items-center space-x-3 sm:space-x-4 py-4 hover:bg-neutral-100 dark:hover:bg-neutral-700 cursor-pointer"
              >
              <span className="block text-neutral-400">
                <ClockIcon className="h-4 sm:h-6 w-4 sm:w-6" />
              </span>
              <span className=" block font-medium text-neutral-700 dark:text-neutral-200">
                {item}
              </span>
            </span>
          ))}
        </div>
      </>
    );
  };

  const renderSearchValue = () => {
    return (
      <>
        {suggestions.map((item) => (
          <span
            onClick={() => handleSelectLocation(item)}
            key={item.place_id}
            className="flex px-4 sm:px-8 items-center space-x-3 sm:space-x-4 py-4 hover:bg-neutral-100 dark:hover:bg-neutral-700 cursor-pointer"
          >
            <span className="block text-neutral-400">
              <ClockIcon className="h-4 w-4 sm:h-6 sm:w-6" />
            </span>
            <span className="block font-medium text-neutral-700 dark:text-neutral-200">
              {item.description}
            </span>
          </span>
        ))}
      </>
    );
  };

  useEffect(() => {
    if (value === "") {
      setShowPopover(false)
    }
  }, [value])

  return (
    <div className={`relative flex ${className}`} ref={containerRef}>
      <div
        onClick={() => setShowPopover(true)}
        className={`flex z-10 flex-1 relative [ nc-hero-field-padding ] flex-shrink-0 items-center space-x-3 cursor-pointer focus:outline-none text-left  ${
          showPopover ? "nc-hero-field-focused" : ""
        }`}
      >
        <div className="text-neutral-300 dark:text-neutral-400">
          <MapPinIcon className="w-5 h-5 lg:w-7 lg:h-7" />
        </div>
        <div className="flex-grow">
          <input
            className={`block w-full bg-transparent border-none focus:ring-0 p-0 focus:outline-none focus:placeholder-neutral-300 xl:text-lg font-semibold placeholder-neutral-800 dark:placeholder-neutral-200 truncate`}
            placeholder={placeHolder}
            value={value}
            autoFocus={showPopover}
            onChange={(e) => {
              const newValue = e.currentTarget.value
              setValue(newValue)
              onChange && onChange(newValue)
              if (newValue === "") {
                setShowPopover(false)
              } else {
                setShowPopover(true)
              }
            }}
            ref={inputRef}
          />
          <span className="block mt-0.5 text-sm text-neutral-400 font-light ">
            <span className="line-clamp-1">{!!value ? placeHolder : desc}</span>
          </span>
          {value && showPopover && (
            <ClearDataButton
              onClick={() => {
                setValue("");
                onChange && onChange("")
              }}
            />
          )}
        </div>
      </div>

      {showPopover && (
        <div
          className={`h-8 absolute self-center top-1/2 -translate-y-1/2 z-0 bg-white dark:bg-neutral-800 ${divHideVerticalLineClass}`}
        ></div>
      )}

      {showPopover && (
        <div className="absolute left-0 z-40 w-full min-w-[300px] sm:min-w-[500px] bg-white dark:bg-neutral-800 top-full mt-3 py-3 sm:py-6 rounded-3xl shadow-xl max-h-96 overflow-y-auto">
          {value ? renderSearchValue() : renderRecentSearches()}
        </div>
      )}
    </div>
  );
};

export default LocationInput;