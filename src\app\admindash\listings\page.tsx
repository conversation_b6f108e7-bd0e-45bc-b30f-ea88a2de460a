"use client"

import type React from "react"
import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ListingDialog } from "@/components/dashboard/listing-dialog"
import { MoreHorizontal, Search, ExternalLink, Edit, Trash, Loader2, X } from "lucide-react"
import type { ListingData, PaginatedResponse } from "@/data/types"
import Link from "next/link"
import Image from "next/image"
import { useDebouncedSearch } from "@/hooks/use-debounced-search"

export default function ListingsPage() {
    const [listings, setListings] = useState<ListingData[]>([])
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [selectedListing, setSelectedListing] = useState<ListingData | null>(null)
    const [isDialogOpen, setIsDialogOpen] = useState(false)
    const [statusFilter, setStatusFilter] = useState("all")
    const [searchType, setSearchType] = useState<"title" | "host">("title")
    const [pagination, setPagination] = useState({
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
    })

    // Create a search function that we'll pass to our custom hook
    const searchListings = useCallback(
        async (query: string) => {
            const params = new URLSearchParams()

            if (statusFilter !== "all") {
                params.append("status", statusFilter)
            }

            if (query) {
                params.append("search", query)
                params.append("searchType", searchType)
            }

            // Use page 1 for new searches
            params.append("page", "1")
            params.append("limit", pagination.limit.toString())

            const response = await fetch(`/api/admindash/listings?${params.toString()}`)

            if (!response.ok) {
                const errorData = await response.json()
                throw new Error(errorData.error || "Failed to fetch listings")
            }

            const result: PaginatedResponse<ListingData> = await response.json()

            // Update pagination state
            setPagination((prevPagination) => ({
                ...prevPagination,
                page: 1,
                total: result.total,
                totalPages: result.totalPages,
            }))

            return result.data
        },
        [statusFilter, searchType, pagination.limit],
    )

    // Use our improved custom hook
    const {
        searchTerm,
        setSearchTerm,
        results: searchResults,
        isSearching,
        clearSearch,
    } = useDebouncedSearch<ListingData[]>(searchListings, 800)

    // Fetch listings when page, status filter changes, or when not searching
    const fetchListings = useCallback(async () => {
        // Skip if we're currently searching
        if (isSearching) return

        try {
            setLoading(true)
            const params = new URLSearchParams()

            if (statusFilter !== "all") {
                params.append("status", statusFilter)
            }

            // Always use the current pagination page
            params.append("page", pagination.page.toString())
            params.append("limit", pagination.limit.toString())

            // If there's a search term, include it in the fetch
            if (searchTerm) {
                params.append("search", searchTerm)
                params.append("searchType", searchType)
            }

            const response = await fetch(`/api/admindash/listings?${params.toString()}`)

            if (!response.ok) {
                const errorData = await response.json()
                throw new Error(errorData.error || "Failed to fetch listings")
            }

            const result: PaginatedResponse<ListingData> = await response.json()
            setListings(result.data)
            setPagination((prev) => ({
                ...prev,
                total: result.total,
                totalPages: result.totalPages,
            }))
        } catch (err: any) {
            console.error("Listings error:", err)
            setError(err.message)
        } finally {
            setLoading(false)
        }
    }, [statusFilter, pagination.page, pagination.limit, searchTerm, searchType, isSearching])

    useEffect(() => {
        fetchListings()
    }, [fetchListings])

    // Update listings when search results change
    useEffect(() => {
        if (searchResults) {
            setListings(searchResults)
        }
    }, [searchResults])

    const handleStatusFilterChange = (value: string) => {
        setStatusFilter(value)
        // Reset search when filter changes
        setSearchTerm("")
        // Reset to page 1 when filter changes
        setPagination({ ...pagination, page: 1 })
    }

    const handleSearchTypeChange = (value: "title" | "host") => {
        setSearchType(value)
        // Reset search when search type changes
        setSearchTerm("")
    }

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value)
    }

    // Handle clear search with explicit refetch
    const handleClearSearch = () => {
        clearSearch()
        // Reset to page 1
        setPagination({ ...pagination, page: 1 })
        // We need to wait for the state to update before fetching
        setTimeout(() => {
            fetchListings()
        }, 0)
    }

    const getStatusBadge = (status: string | undefined) => {
        if (!status) return null

        switch (status) {
            case "active":
                return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-500 text-white">Active</span>
            case "pending":
                return (
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-500 text-white whitespace-nowrap">
            En attente
          </span>
                )
            case "rejected":
                return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-red-500 text-white">Rejetée</span>
            case "draft":
                return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-gray-500 text-white">Brouillon</span>
            default:
                return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-gray-500 text-white">{status}</span>
        }
    }

    const handleEdit = (listing: ListingData) => {
        setSelectedListing(listing)
        setIsDialogOpen(true)
    }

    const handleApprove = async (listing: ListingData) => {
        try {
            const response = await fetch(`/api/admindash/listings/${listing.propertyId}/status`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ status: "active" }),
            })

            if (!response.ok) {
                const errorData = await response.json()
                throw new Error(errorData.error || "Failed to approve listing")
            }

            // Refetch listings to update the UI
            fetchListings()
        } catch (err: any) {
            console.error("Approve listing error:", err)
            setError(err.message)
        }
    }

    const handleReject = async (listing: ListingData) => {
        try {
            const response = await fetch(`/api/admindash/listings/${listing.propertyId}/status`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ status: "rejected" }),
            })

            if (!response.ok) {
                const errorData = await response.json()
                throw new Error(errorData.error || "Failed to reject listing")
            }

            // Refetch listings to update the UI
            fetchListings()
        } catch (err: any) {
            console.error("Reject listing error:", err)
            setError(err.message)
        }
    }

    const handleDelete = async (id: string | undefined) => {
        if (!id) return

        try {
            const response = await fetch(`/api/admindash/listings/${id}`, {
                method: "DELETE",
            })

            if (!response.ok) {
                const errorData = await response.json()
                throw new Error(errorData.error || "Failed to delete listing")
            }

            // Refetch listings to update the UI
            fetchListings()
        } catch (err: any) {
            console.error("Delete listing error:", err)
            setError(err.message)
        }
    }

    const getListingDetailUrl = (id: string | undefined) => {
        if (!id) {
            console.error("Listing ID is undefined")
            return "#" // Return a safe fallback URL
        }
        return `/listing-stay-detail/${id}`
    }

    const isLoadingData = loading || isSearching

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold">Annonces</h1>
            </div>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                    <div className="relative flex-1">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
                        <Input
                            placeholder={searchType === "title" ? "Rechercher des annonces..." : "Rechercher par hôte..."}
                            className="w-full sm:w-[300px] pl-8 pr-10"
                            value={searchTerm}
                            onChange={handleSearchChange}
                        />
                        {isSearching ? (
                            <div className="absolute right-2.5 top-2.5">
                                <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                            </div>
                        ) : searchTerm ? (
                            <Button
                                variant="ghost"
                                size="icon"
                                className="absolute right-1 top-1 h-6 w-6 text-gray-400 hover:text-gray-600"
                                onClick={handleClearSearch}
                            >
                                <X className="h-4 w-4" />
                                <span className="sr-only">Clear search</span>
                            </Button>
                        ) : null}
                    </div>
                    <Select value={searchType} onValueChange={(value) => handleSearchTypeChange(value as "title" | "host")}>
                        <SelectTrigger className="w-full sm:w-[180px]">
                            <SelectValue placeholder="Type de recherche" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="title">Recherche par titre</SelectItem>
                            <SelectItem value="host">Recherche par hôte</SelectItem>
                        </SelectContent>
                    </Select>
                    <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
                        <SelectTrigger className="w-full sm:w-[180px]">
                            <SelectValue placeholder="Tous les statuts" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">Tous les statuts</SelectItem>
                            <SelectItem value="pending">En attente</SelectItem>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="rejected">Rejetée</SelectItem>
                            <SelectItem value="draft">Brouillon</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
            </div>
            {error && <div className="p-4 mb-4 text-sm text-red-700 bg-red-100 rounded-lg">Erreur: {error}</div>}
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Image</TableHead>
                            <TableHead>Titre</TableHead>
                            <TableHead>Hôte</TableHead>
                            <TableHead>Emplacement</TableHead>
                            <TableHead>Statut</TableHead>
                            <TableHead>Créée le</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoadingData ? (
                            // Show loading rows
                            Array(5)
                                .fill(0)
                                .map((_, index) => (
                                    <TableRow key={`loading-${index}`}>
                                        <TableCell colSpan={7} className="h-16">
                                            <div className="flex items-center space-x-4">
                                                <div className="h-12 w-16 bg-gray-200 rounded animate-pulse"></div>
                                                <div className="space-y-2 flex-1">
                                                    <div className="h-4 bg-gray-200 rounded w-1/3 animate-pulse"></div>
                                                    <div className="h-4 bg-gray-200 rounded w-1/4 animate-pulse"></div>
                                                </div>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))
                        ) : listings.length > 0 ? (
                            listings.map((listing) => (
                                <TableRow key={listing.propertyId}>
                                    <TableCell>
                                        <Link href={getListingDetailUrl(listing.propertyId)} target="_blank">
                                            <div className="relative h-12 w-16 overflow-hidden rounded-md cursor-pointer transition-opacity hover:opacity-80">
                                                {listing.coverImageUrl && !listing.coverImageUrl.startsWith("blob:") ? (
                                                    <Image
                                                        src={listing.coverImageUrl || "/placeholder.svg"}
                                                        alt={listing.propertyTitle || ""}
                                                        fill
                                                        sizes="64px"
                                                        className="object-cover"
                                                        onError={(e) => {
                                                            // Handle image load error
                                                            const target = e.target as HTMLImageElement
                                                            target.src = "/placeholder.svg"
                                                        }}
                                                    />
                                                ) : (
                                                    <div className="flex h-full w-full items-center justify-center bg-gray-100 text-gray-400">
                                                        Pas d&apos;image
                                                    </div>
                                                )}
                                            </div>
                                        </Link>
                                    </TableCell>
                                    <TableCell className="font-medium">
                                        <Link
                                            href={getListingDetailUrl(listing.propertyId)}
                                            target="_blank"
                                            className="hover:text-blue-600 hover:underline cursor-pointer transition-colors"
                                        >
                                            {listing.propertyTitle}
                                        </Link>
                                    </TableCell>
                                    <TableCell>{listing.host || "Inconnu"}</TableCell>
                                    <TableCell>
                                        {listing.propertyCity
                                            ? `${listing.propertyCity}, ${listing.propertyState || ""}`
                                            : "Emplacement inconnu"}
                                    </TableCell>
                                    <TableCell>{getStatusBadge(listing.propertyStatus)}</TableCell>
                                    <TableCell>{listing.createdAt ? new Date(listing.createdAt).toLocaleDateString() : ""}</TableCell>
                                    <TableCell className="text-right">
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" className="h-8 w-8 p-0">
                                                    <span className="sr-only">Ouvrir le menu</span>
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem asChild>
                                                    <Link
                                                        href={getListingDetailUrl(listing.propertyId)}
                                                        target="_blank"
                                                        className="flex items-center"
                                                    >
                                                        <ExternalLink className="mr-2 h-4 w-4" />
                                                        Voir
                                                    </Link>
                                                </DropdownMenuItem>
                                                <DropdownMenuItem asChild>
                                                    <Link href={`/admindash/listings/edit/${listing.propertyId}`} className="flex items-center">
                                                        <Edit className="mr-2 h-4 w-4" />
                                                        Modification avancée
                                                    </Link>
                                                </DropdownMenuItem>
                                                <DropdownMenuItem onClick={() => handleEdit(listing)}>Modification rapide</DropdownMenuItem>
                                                <DropdownMenuItem onClick={() => handleApprove(listing)}>Approuver</DropdownMenuItem>
                                                <DropdownMenuItem onClick={() => handleReject(listing)}>Rejeter</DropdownMenuItem>
                                                <DropdownMenuItem onClick={() => handleDelete(listing.propertyId)} className="text-red-600">
                                                    <Trash className="mr-2 h-4 w-4" />
                                                    Supprimer
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={7} className="h-24 text-center">
                                    {searchTerm ? "Aucune annonce trouvée pour cette recherche." : "Aucune annonce trouvée."}
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>

            {/* Pagination - show for both regular listings and search results */}
            {pagination.totalPages > 1 && (
                <div className="flex items-center justify-center space-x-2">
                    <Button
                        variant="outline"
                        onClick={() => setPagination({ ...pagination, page: Math.max(1, pagination.page - 1) })}
                        disabled={pagination.page === 1 || isLoadingData}
                    >
                        Précédent
                    </Button>
                    <span>
            Page {pagination.page} sur {pagination.totalPages}
          </span>
                    <Button
                        variant="outline"
                        onClick={() => setPagination({ ...pagination, page: Math.min(pagination.totalPages, pagination.page + 1) })}
                        disabled={pagination.page === pagination.totalPages || isLoadingData}
                    >
                        Suivant
                    </Button>
                </div>
            )}

            <ListingDialog
                open={isDialogOpen}
                onOpenChange={setIsDialogOpen}
                listing={selectedListing}
                onSave={fetchListings}
            />
        </div>
    )
}
