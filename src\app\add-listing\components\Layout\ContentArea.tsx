"use client";

import React from "react";
import { useParams } from "next/navigation";

interface ContentAreaProps {
    children: React.ReactNode;
}

export const ContentArea: React.FC<ContentAreaProps> = ({ children }) => {
    const params = useParams();
    const stepIndex = params?.stepIndex?.[0] || "1";
    
    // Enable scrolling specifically for step 7
    const shouldEnableScroll = stepIndex === "7";

    return (
        <div className={`p-2 sm:p-3 md:p-4 lg:p-5 ${shouldEnableScroll ? 'overflow-auto' : ''}`}>
            {children}
        </div>
    );
};

export default ContentArea;