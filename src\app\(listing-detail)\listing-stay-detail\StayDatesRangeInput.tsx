"use client"

import { Fragment, useState, type FC, useEffect } from "react"
import { Popover, Transition } from "@headlessui/react"
import { CalendarIcon } from "@heroicons/react/24/outline"
import DatePickerCustomHeaderTwoMonth from "@/components/DatePickerCustomHeaderTwoMonth"
import DatePickerCustomDay from "@/app/(listing-detail)/(components)/DatePickerCustomDay"
import DatePicker from "react-datepicker"
import ClearDataButton from "@/app/(client-components)/(HeroSearchForm)/ClearDataButton"
import { createClient } from "@/utils/supabase/client"
import { parseDate } from "@/utils/dateUtils"
import { Input } from "@/components/ui/input"

export interface StayDatesRangeInputProps {
  className?: string
  basePrice: number
  listingId: string
  initialDates?: {
    startDate: Date | null
    endDate: Date | null
  }
  onDatesChange?: (dates: {
    startDate: Date | null
    endDate: Date | null
    numberOfNights: number
    totalPrice: number
    priceBreakdown: {
      basePrice: number
      totalNights: number
      totalPrice: number
    }
  }) => void
}

const StayDatesRangeInput: FC<StayDatesRangeInputProps> = ({
                                                             className = "flex-1",
                                                             basePrice,
                                                             listingId,
                                                             initialDates,
                                                             onDatesChange,
                                                           }) => {
  const [startDate, setStartDate] = useState<Date | null>(initialDates?.startDate || null)
  const [endDate, setEndDate] = useState<Date | null>(initialDates?.endDate || null)
  const [excludedRanges, setExcludedRanges] = useState<{ start: Date; end: Date }[]>([])
  const [showAffiliateInput, setShowAffiliateInput] = useState(false)
  const [affiliateCode, setAffiliateCode] = useState("")
  const [isValidatingCode, setIsValidatingCode] = useState(false)
  const [isCodeValid, setIsCodeValid] = useState<boolean | null>(null)

  useEffect(() => {
    const fetchAvailability = async () => {
      const supabase = createClient()
      const { data: availabilityData, error } = await supabase
          .from("listing_availability")
          .select("start_date, end_date")
          .eq("listing_id", listingId)

      if (error) {
        console.error("Error fetching availability:", error)
        return
      }

      if (availabilityData && availabilityData.length > 0) {
        const ranges = availabilityData.map((period) => ({
          start: parseDate(period.start_date),
          end: parseDate(period.end_date),
        }))
        setExcludedRanges(ranges)
      }
    }

    fetchAvailability()
  }, [listingId])

  useEffect(() => {
    if (initialDates?.startDate && initialDates?.endDate) {
      setStartDate(initialDates.startDate);
      setEndDate(initialDates.endDate);
    }
  }, [initialDates]);

  useEffect(() => {
    if (startDate && endDate) {
      const numberOfNights = Math.ceil(
        (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
      );
      const totalPrice = basePrice * numberOfNights;
      
      onDatesChange?.({
        startDate,
        endDate,
        numberOfNights,
        totalPrice,
        priceBreakdown: {
          basePrice,
          totalNights: numberOfNights,
          totalPrice,
        },
      });
    }
  }, [startDate, endDate, basePrice, onDatesChange]);

  const calculatePricing = (start: Date | null, end: Date | null) => {
    if (!start || !end) {
      return {
        numberOfNights: 0,
        totalPrice: 0,
        priceBreakdown: {
          basePrice,
          totalNights: 0,
          totalPrice: 0,
        },
      }
    }

    const numberOfNights = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
    // Apply 5% increase to base price silently and round up
    const increasedBasePrice = Math.ceil(basePrice * 1.05)
    const baseAmount = numberOfNights * increasedBasePrice
    // Apply 2% service fee and round up
    const serviceFee = Math.ceil(baseAmount * 0.02)
    const totalPrice = baseAmount + serviceFee

    return {
      numberOfNights,
      totalPrice,
      priceBreakdown: {
        basePrice: increasedBasePrice,
        totalNights: numberOfNights,
        totalPrice,
        serviceFee,
      },
    }
  }

  const isRangeValid = (start: Date, end: Date) => {
    return !excludedRanges.some(
        (range) =>
            (start < range.start && end > range.start) ||
            (start < range.end && end > range.end) ||
            (start >= range.start && end <= range.end),
    )
  }

  const onChangeDate = (dates: [Date | null, Date | null], close: () => void) => {
    const [start, end] = dates

    if (start && end && !isRangeValid(start, end)) {
      // If the range is not valid, only set the start date
      setStartDate(start)
      setEndDate(null)
      onDatesChange?.({
        startDate: start,
        endDate: null,
        ...calculatePricing(start, null),
      })
    } else {
      setStartDate(start)
      setEndDate(end)
      const pricing = calculatePricing(start, end)
      onDatesChange?.({
        startDate: start,
        endDate: end,
        ...pricing,
      })

      // Close the popover if both dates are selected
      if (start && end) {
        setTimeout(() => close(), 300)
      }
    }
  }

  const formatDate = (date: Date | null) => {
    if (!date) return ""
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "2-digit",
    })
  }

  const isDateExcluded = (date: Date) => {
    return excludedRanges.some((range) => {
      const checkDate = new Date(date.getTime())
      checkDate.setHours(0, 0, 0, 0)
      const startDate = new Date(range.start.getTime())
      startDate.setHours(0, 0, 0, 0)
      const endDate = new Date(range.end.getTime())
      endDate.setHours(0, 0, 0, 0)
      return checkDate >= startDate && checkDate <= endDate
    })
  }

  const renderInput = () => {
    return (
        <>
          <div className="text-neutral-300 dark:text-neutral-400">
            <CalendarIcon className="w-5 h-5 lg:w-7 lg:h-7" />
          </div>
          <div className="flex-grow text-left">
          <span className="block xl:text-lg font-semibold">
            {startDate ? formatDate(startDate) : "Ajouter des dates"}
            {endDate ? ` - ${formatDate(endDate)}` : ""}
          </span>
            <span className="block mt-1 text-sm text-neutral-400 leading-none font-light">
            {startDate && endDate ? `${calculatePricing(startDate, endDate).numberOfNights} nuits` : "Arrivée - Départ"}
          </span>
          </div>
        </>
    )
  }

  const handleAffiliateCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAffiliateCode(e.target.value)
    setIsCodeValid(null)
  }

  const validateAffiliateCode = async () => {
    if (!affiliateCode.trim()) return

    setIsValidatingCode(true)

    try {
      // Call your API to validate the code
      const response = await fetch("/api/validate-affiliate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ code: affiliateCode.trim(), listingId }),
      })

      const data = await response.json()

      if (data.success) {
        setIsCodeValid(true)
        // Store in localStorage or context for later use during booking
        localStorage.setItem("affiliate_code", affiliateCode.trim())
      } else {
        setIsCodeValid(false)
      }
    } catch (error) {
      console.error("Error validating affiliate code:", error)
      setIsCodeValid(false)
    } finally {
      setIsValidatingCode(false)
    }
  }

  return (
      <Popover className={`StayDatesRangeInput z-10 relative flex ${className}`}>
        {({ open, close }) => (
            <>
              <Popover.Button
                  className={`flex-1 flex relative p-3 items-center space-x-3 focus:outline-none ${open ? "shadow-lg" : ""}`}
              >
                {renderInput()}
                {startDate && open && <ClearDataButton onClick={() => onChangeDate([null, null], close)} />}
              </Popover.Button>

              <Transition
                  as={Fragment}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 translate-y-1"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 translate-y-1"
              >
                <Popover.Panel className="absolute left-auto xl:-right-10 right-0 z-10 mt-3 top-full w-screen max-w-sm px-4 sm:px-0 lg:max-w-3xl">
                  <div className="overflow-hidden rounded-3xl shadow-lg ring-1 ring-black ring-opacity-5 bg-white dark:bg-neutral-800 p-8">
                    <div className="text-neutral-500 dark:text-neutral-400 text-sm mb-4">
                      {startDate && endDate && (
                          <div className="space-y-2">
                            <p>Séjour sélectionné : {calculatePricing(startDate, endDate).numberOfNights} nuits</p>
                            <p>Prix par nuit : {Math.ceil(basePrice * 1.05)} TND</p>
                            <p>Sous-total : {calculatePricing(startDate, endDate).numberOfNights * Math.ceil(basePrice * 1.05)} TND</p>
                            <p>Frais de service (2%) : {calculatePricing(startDate, endDate).priceBreakdown.serviceFee} TND</p>
                            <div className="border-t pt-2 mt-2">
                              <p className="font-semibold">Total : {calculatePricing(startDate, endDate).totalPrice} TND</p>
                            </div>
                          </div>
                      )}
                    </div>
                    <DatePicker
                        selected={startDate}
                        onChange={(dates) => onChangeDate(dates as [Date | null, Date | null], close)}
                        startDate={startDate}
                        endDate={endDate}
                        selectsRange
                        monthsShown={2}
                        showPopperArrow={false}
                        inline
                        minDate={new Date()}
                        excludeDates={excludedRanges.flatMap((range) => {
                          const dates = []
                          const currentDate = new Date(range.start)
                          while (currentDate <= range.end) {
                            dates.push(new Date(currentDate))
                            currentDate.setDate(currentDate.getDate() + 1)
                          }
                          return dates
                        })}
                        renderCustomHeader={(p) => <DatePickerCustomHeaderTwoMonth {...p} />}
                        renderDayContents={(day, date) =>
                            (
                                <DatePickerCustomDay
                                    dayOfMonth={day}
                                    date={date}
                                    isUnavailable={date ? isDateExcluded(date) : false}
                                />
                            ) as any
                        }
                    />
                    <div className="mt-4 mb-4">
                      {!showAffiliateInput ? (
                        <button 
                          type="button"
                          onClick={() => setShowAffiliateInput(true)}
                          className="text-sm text-booking-orange hover:underline"
                        >
                          Vous avez un code de réduction ?
                        </button>
                      ) : (
                        <div className="space-y-2">
                          <div className="flex gap-2">
                            <Input
                              value={affiliateCode}
                              onChange={handleAffiliateCodeChange}
                              placeholder="Entrer un code promo"
                              className="flex-1"
                              disabled={isValidatingCode || isCodeValid === true}
                            />
                            <button 
                              type="button"
                              onClick={validateAffiliateCode}
                              disabled={isValidatingCode || !affiliateCode.trim() || isCodeValid === true}
                              className={`px-3 py-1 rounded text-sm ${
                                isCodeValid === true 
                                  ? "bg-green-100 text-green-800 border border-green-300" 
                                  : "bg-booking-orange text-white"
                              }`}
                            >
                              {isValidatingCode ? "..." : isCodeValid === true ? "✓" : "Appliquer"}
                            </button>
                          </div>
                          
                          {isCodeValid !== null && (
                            <div className={`flex items-center text-sm ${isCodeValid ? "text-green-600" : "text-red-600"}`}>
                              {isCodeValid ? "Code valide" : "Code invalide"}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </Popover.Panel>
              </Transition>
            </>
        )}
      </Popover>
  )
}

export default StayDatesRangeInput
