"use client"

import { Fragment, useState, useEffect, useRef, type FC } from "react"
import { Popover, Transition } from "@headlessui/react"
import DatePickerCustomHeaderTwoMonth from "@/components/DatePickerCustomHeaderTwoMonth"
import DatePickerCustomDay from "@/components/DatePickerCustomDay"
import DatePicker from "react-datepicker"
import ClearDataButton from "../ClearDataButton"
import { useSearch } from "@/app/(stay-listings)/SearchContext"

export interface StayDatesRangeInputProps {
    className?: string
    fieldClassName?: string
    onDateChange?: (startDate: Date | null, endDate: Date | null) => void
    startDate?: Date | null
    endDate?: Date | null
}

const StayDatesRangeInput: FC<StayDatesRangeInputProps> = ({
                                                               className = "[ lg:nc-flex-2 ]",
                                                               fieldClassName = "[ nc-hero-field-padding--small ]",
                                                               onDateChange,
                                                               startDate: propStartDate = null,
                                                               endDate: propEndDate = null,
                                                           }) => {
    // Use SearchContext for date state
    const { searchParams, setSearchParams } = useSearch()
    
    // Initialize with either props or context values
    const [startDate, setStartDate] = useState<Date | null>(propStartDate || searchParams.checkIn)
    const [endDate, setEndDate] = useState<Date | null>(propEndDate || searchParams.checkOut)
    
    const popoverRef = useRef<HTMLButtonElement>(null)

    // Sync with prop changes and ensure SearchContext is updated
    useEffect(() => {
        if (propStartDate !== startDate) {
            setStartDate(propStartDate)
            
            // Update SearchContext if props changed from outside
            if (propStartDate !== searchParams.checkIn) {
                setSearchParams(prev => ({
                    ...prev,
                    checkIn: propStartDate
                }))
            }
        }
        
        if (propEndDate !== endDate) {
            setEndDate(propEndDate)
            
            // Update SearchContext if props changed from outside
            if (propEndDate !== searchParams.checkOut) {
                setSearchParams(prev => ({
                    ...prev,
                    checkOut: propEndDate
                }))
            }
        }
    }, [propStartDate, propEndDate])

    // Sync with SearchContext changes
    useEffect(() => {
        if (searchParams.checkIn !== startDate) {
            setStartDate(searchParams.checkIn)
        }
        
        if (searchParams.checkOut !== endDate) {
            setEndDate(searchParams.checkOut)
        }
    }, [searchParams.checkIn, searchParams.checkOut])

    const closePopover = () => {
        if (popoverRef.current) {
            const closeEvent = new MouseEvent("click", {
                view: window,
                bubbles: true,
                cancelable: true,
            })
            popoverRef.current.dispatchEvent(closeEvent)
        }
    }

    const onChangeDate = (dates: [Date | null, Date | null]) => {
        const [start, end] = dates
        setStartDate(start)
        setEndDate(end)
        
        // Update local callback if provided
        onDateChange && onDateChange(start, end)
        
        // Always update SearchContext
        setSearchParams(prev => ({
            ...prev,
            checkIn: start,
            checkOut: end
        }))

        // Close the popover when both start and end dates are selected
        if (start && end) {
            closePopover()
        }
    }

    const renderInput = () => {
        return (
            <>
                <div className="flex-grow text-left">
          <span className="block xl:text-base font-semibold">
            {startDate?.toLocaleDateString("en-US", {
                month: "short",
                day: "2-digit",
            }) || "Ajouter des dates"}
              {endDate
                  ? " - " +
                  endDate?.toLocaleDateString("en-US", {
                      month: "short",
                      day: "2-digit",
                  })
                  : ""}
          </span>
                    <span className="block mt-1 text-sm text-neutral-400 leading-none font-light">{"Arrivée - Départ"}</span>
                </div>
            </>
        )
    }

    return (
        <Popover className={`StayDatesRangeInput z-10 relative flex ${className}`}>
            {({ open }) => (
                <>
                    <Popover.Button
                        ref={popoverRef}
                        className={`flex-1 z-10 flex relative ${fieldClassName} items-center space-x-3 focus:outline-none ${
                            open ? "nc-hero-field-focused--2" : ""
                        }`}
                    >
                        {renderInput()}
                        {startDate && open && <ClearDataButton onClick={() => onChangeDate([null, null])} />}
                    </Popover.Button>

                    {open && (
                        <div className="h-8 absolute self-center top-1/2 -translate-y-1/2 z-0 -inset-x-0.5 bg-white dark:bg-neutral-800"></div>
                    )}

                    <Transition
                        as={Fragment}
                        enter="transition ease-out duration-200"
                        enterFrom="opacity-0 translate-y-1"
                        enterTo="opacity-100 translate-y-0"
                        leave="transition ease-in duration-150"
                        leaveFrom="opacity-100 translate-y-0"
                        leaveTo="opacity-0 translate-y-1"
                    >
                        <Popover.Panel className="absolute left-1/2 z-10 mt-3 top-full w-screen max-w-sm -translate-x-1/2 transform px-4 sm:px-0 lg:max-w-3xl">
                            <div className="overflow-hidden rounded-3xl shadow-lg ring-1 ring-black ring-opacity-5 bg-white dark:bg-neutral-800 p-8">
                                <DatePicker
                                    selected={startDate}
                                    onChange={onChangeDate}
                                    startDate={startDate}
                                    endDate={endDate}
                                    selectsRange
                                    monthsShown={2}
                                    showPopperArrow={false}
                                    inline
                                    renderCustomHeader={(p) => <DatePickerCustomHeaderTwoMonth {...p} />}
                                    renderDayContents={(day, date) => <DatePickerCustomDay dayOfMonth={day} date={date} />}
                                />
                            </div>
                        </Popover.Panel>
                    </Transition>
                </>
            )}
        </Popover>
    )
}

export default StayDatesRangeInput

