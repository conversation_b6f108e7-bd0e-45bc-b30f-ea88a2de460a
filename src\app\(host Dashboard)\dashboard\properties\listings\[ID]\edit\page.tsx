"use client"

import { useState, useEffect } from "react"
import { useRouter, useParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import CustomSelect from "@/components/CustomSelect"
import { 
  DescriptionTab, 
  RoomsAndPeopleTab, 
  MediaTab, 
  LocationTab, 
  PricingTab,
  AmenitiesTab, 
  RulesTab, 
  CancellationTab,
  FeeModal,
  TimeStayModal,
  PropertyData
} from "@/components/edit-listing"

export default function PropertyContent() {
  const router = useRouter()
  const params = useParams()
  const id = params.ID

  // Main state and UI state
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("description")
  
  // Form validation states
  const [titleError, setTitleError] = useState<string | null>(null)
  const [descriptionError, setDescriptionError] = useState<string | null>(null)
  const [locationErrors, setLocationErrors] = useState<{ state?: string; city?: string; address?: string }>({})
  
  // Media related states
  const [pendingCoverImage, setPendingCoverImage] = useState<File | null>(null);
  const [pendingCoverImageUrl, setPendingCoverImageUrl] = useState<string | null>(null);
  const [pendingImages, setPendingImages] = useState<File[]>([]);
  
  // Modal states
  const [isFeeModalOpen, setIsFeeModalOpen] = useState(false);
  const [feeModalData, setFeeModalData] = useState<{
    title: string;
    price: number;
    feeType: string;
    isOptional: boolean;
    editingIndex: number | null;
  }>({
    title: "",
    price: 0,
    feeType: "",
    isOptional: true,
    editingIndex: null,
  });
  
  const [isTimeStayModalOpen, setIsTimeStayModalOpen] = useState(false);
  const [timeStayData, setTimeStayData] = useState({
    arrivalTime: "",
    departureTime: "",
    minStay: 0,
    maxStay: 0,
  });

  // Main property data
  const [propertyData, setPropertyData] = useState<PropertyData>({
    id: "",
    title: "",
    description: "",
    categoryType: "bungalow",
    propertyType: "shared",
    pricing: {
      nightlyRate: 0,
      minStay: 0,
      maxStay: 0,
      additionalFees: [],
    },
    pricePerWeek: 0,
    minStay: 0,
    maxStay: 0,
    extraCharges: [],
    additional_fees: [],
    amenities: [],
    images: [],
    videoOption: "youtube",
    videoId: "",
    virtualTourUrl: "",
    location: {
      lat: 0,
      lng: 0,
      state: {
        id: "",
        name: "",
      },
      stateId: "",
      city: {
        id: "",
        name: "",
      },
      cityId: "",
      address: "",
    },
    paymentStatus: "instant",
    num_guests: 1,
    num_bedrooms: 1,
    num_beds: 1,
    num_bathrooms: 1,
    num_kitchens: 0,
    coverImageUrl: "",
    arrivalTime: "",
    departureTime: "",
    houseRules: [],
    pets: "allow",
    parties: "allow",
    amenitiesToggle: "allow",
    cooking: "allow",
    cancellationPolicyId: "",
    cancellationPolicy: null,
  })

  // Tab options for navigation
  const tabOptions = [
    { id: "description", name: "Description" },
    { id: "personnes-chambres", name: "Personnes et chambres" },
    { id: "media", name: "Media" },
    { id: "location", name: "Location" },
    { id: "prix", name: "Prix" },
    { id: "amenities", name: "Équipements" },
    { id: "regles", name: "Règles" },
    { id: "annulation", name: "Annulation" },
  ];

  useEffect(() => {
    const fetchPropertyData = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/listing_details/${params.ID}`)

        if (!response.ok) {
          throw new Error("Failed to fetch property details")
        }
        const data = await response.json()

        if (data.success && data.listing) {
          // Transform the API response to match PropertyData interface
          const transformedData: PropertyData = {
            id: data.listing.propertyId || "",
            title: data.listing.propertyTitle || "",
            description: data.listing.propertyDescription || "",
            categoryType: data.listing.propertyCategory?.toLowerCase() || "bungalow",
            propertyType: data.listing.propertyType?.toLowerCase() || "shared",
            pricing: {
              nightlyRate: data.listing.pricing.nightlyRate || 0,
              minStay: data.listing.pricing.minStay || 0,
              maxStay: data.listing.pricing.maxStay || null,
              additionalFees: data.listing.pricing.additionalFees || [],
              discounts: data.listing.pricing.discounts || [],
            },
            roomTypeId: data.listing.roomTypeId,
            pricePerWeek: 0,
            minStay: data.listing.pricing.minStay || 0,
            maxStay: data.listing.pricing.maxStay || 0,
            extraCharges: data.listing.extraCharges || [],
            amenities: data.listing.amenities?.map((a: any) => a.id) || [],
            images: data.listing.placeImagesUrl || [],
            videoOption: "youtube",
            videoId: "",
            virtualTourUrl: "",
            location: {
              lat: Number.parseFloat(data.listing.location?.latitude) || 0,
              lng: Number.parseFloat(data.listing.location?.longitude) || 0,
              state: {
                id: data.listing.stateId || "",
                name: data.listing.propertyState || "",
              },
              stateId: data.listing.stateId || "",
              city: {
                id: data.listing.cityId || "",
                name: data.listing.propertyCity || "",
              },
              cityId: data.listing.cityId || "",
              address: data.listing.propertyAddress || "",
            },
            paymentStatus: data.listing.status?.toLowerCase() || "instant",
            additional_fees: [],
            num_guests: data.listing.numGuests || 1,
            num_bedrooms: data.listing.numBedrooms || 1,
            num_beds: data.listing.numBeds || 1,
            num_bathrooms: data.listing.numBathrooms || 1,
            num_kitchens: data.listing.numKitchens || 0,
            coverImageUrl: data.listing.coverImageUrl || "",
            arrivalTime: data.listing.pricing.arrivalTime || "",
            departureTime: data.listing.pricing.departureTime || "",
            houseRules: data.listing.houseRules || [],
            pets: data.listing.pets || "allow",
            parties: data.listing.parties || "allow",
            amenitiesToggle: data.listing.amenitiesToggle || "allow",
            cooking: data.listing.cooking || "allow",
            cancellationPolicyId: data.listing.cancellationPolicyId || "",
            cancellationPolicy: data.listing.cancellationPolicy || null,
          }

          setPropertyData(transformedData)
          
          // Initialize time stay data
          setTimeStayData({
            arrivalTime: transformedData.arrivalTime || "",
            departureTime: transformedData.departureTime || "",
            minStay: transformedData.pricing.minStay || 0,
            maxStay: transformedData.pricing.maxStay || 0,
          });
        } else {
          throw new Error(data.error || "Failed to fetch property details")
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred")
        console.error("Error fetching property details:", err)
      } finally {
        setIsLoading(false)
      }
    }

    if (params.ID) {
      fetchPropertyData();
    }
  }, [params.ID])

  const handleSave = async () => {
    try {
      // Validate title and description before saving
      if (propertyData.title.length < 25 || propertyData.title.length > 65) {
        setTitleError("Title must be between 25 and 65 characters long")
        setActiveTab("description")
        return
      }

      if (propertyData.description.length < 25) {
        setDescriptionError("Description must be between 25 ")
        setActiveTab("description")
        return
      }

      setIsLoading(true)
      setError(null)

      if (!params.ID) {
        throw new Error("Property ID is missing")
      }

      let response: Response, result: any;
      const safeUUID = (val: string | null | undefined) => (val && typeof val === 'string' && val.length > 0 ? val : null);
      
      // Check if we have any files to upload
      if (pendingCoverImage || pendingImages.length > 0) {
        // Use FormData for file upload
        const formData = new FormData();
        
        // Add cover image if it exists
        if (pendingCoverImage) {
          formData.append('coverImage', pendingCoverImage);
        }
        
        // Add all pending additional images
        pendingImages.forEach((file, index) => {
          formData.append(`image_${index}`, file);
        });
        
        // Filter out blob URLs from images array before sending
        const filteredImages = propertyData.images.filter(url => !url.startsWith('blob:'));
        
        // Add the JSON property data
        formData.append('propertyData', JSON.stringify({
          ...propertyData,
          id: params.ID,
          images: filteredImages, // Only send actual server URLs, not blob URLs
          amenities: propertyData.amenities,
          extraCharges: propertyData.extraCharges,
          houseRules: propertyData.houseRules,
          pets: propertyData.pets,
          parties: propertyData.parties,
          amenitiesToggle: propertyData.amenitiesToggle,
          cooking: propertyData.cooking,
          location: {
            ...propertyData.location,
            stateId: safeUUID(propertyData.location.stateId),
            cityId: safeUUID(propertyData.location.cityId),
          },
        }));
        
        response = await fetch(`/api/property-update`, {
          method: "PUT",
          body: formData,
        });
      } else {
        // Fallback to JSON if no file
        const dataToSend = {
          ...propertyData,
          id: params.ID,
          amenities: propertyData.amenities,
          extraCharges: propertyData.extraCharges,
          houseRules: propertyData.houseRules,
          pets: propertyData.pets,
          parties: propertyData.parties,
          amenitiesToggle: propertyData.amenitiesToggle,
          cooking: propertyData.cooking,
          location: {
            ...propertyData.location,
            stateId: safeUUID(propertyData.location.stateId),
            cityId: safeUUID(propertyData.location.cityId),
          },
        };
        response = await fetch(`/api/property-update`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(dataToSend),
          cache: "no-store",
        });
      }

      // Check if response is JSON
      const contentType = response.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        const text = await response.text();
        console.error("Received non-JSON response:", text);
        throw new Error("Server returned non-JSON response. Check server logs.");
      }

      result = await response.json();

      if (!response.ok) {
        console.error("Server responded with an error:", result);
        throw new Error(result.error || "Failed to update property");
      }

      // Update frontend state with the new cover image URL if returned
      if (result.coverImageUrl) {
        setPropertyData((prev) => ({ ...prev, coverImageUrl: result.coverImageUrl }));
      }
      
      // Update the images array with new server URLs if returned
      if (result.imageUrls && result.imageUrls.length > 0) {
        setPropertyData((prev) => ({ 
          ...prev, 
          images: [...prev.images.filter(url => !url.startsWith('blob:')), ...result.imageUrls]
        }));
      }
      
      // Clear pending images
      setPendingCoverImage(null);
      setPendingCoverImageUrl(null);
      setPendingImages([]);

      console.log("Property updated successfully:", result);
      setIsLoading(false);
      router.push(`/dashboard/properties/listings/${params.ID}/details`);
    } catch (err) {
      console.error("Error saving property:", err);
      setError(err instanceof Error ? err.message : "An unexpected error occurred");
      setIsLoading(false);
    }
  }

  // Handle modal save actions
  const handleSaveFee = (fee: { title: string; price: number; feeType: string; isOptional: boolean }, editingIndex: number | null) => {
    setPropertyData(prev => {
      const newAdditionalFees = [...(prev.pricing.additionalFees || [])];
      
      if (editingIndex !== null) {
        // Update existing fee
        newAdditionalFees[editingIndex] = fee;
      } else {
        // Add new fee
        newAdditionalFees.push(fee);
      }
      
      return {
        ...prev,
        pricing: {
          ...prev.pricing,
          additionalFees: newAdditionalFees
        }
      };
    });
  };

  const handleSaveTimeStay = (data: typeof timeStayData) => {
    setPropertyData(prev => ({
      ...prev,
      arrivalTime: data.arrivalTime,
      departureTime: data.departureTime,
      pricing: {
        ...prev.pricing,
        minStay: data.minStay,
        maxStay: data.maxStay
      },
      minStay: data.minStay,
      maxStay: data.maxStay
    }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-[#fcfbff]">
        <div className="text-[#64748b] text-xl">chargement des donnees ...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen bg-[#fcfbff]">
        <div className="text-red-500 text-xl">{error}</div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-7xl mx-auto bg-[#fcfbff]">
      <div className="flex justify-between items-center mb-6">
        <h2
          className="w-full max-w-full text-lg sm:text-2xl font-bold text-[#32353e] break-words px-2 line-clamp-2"
          style={{ wordBreak: "break-word" }}
        >
          {propertyData.title}
        </h2>
        {/* Desktop buttons */}
        <div className="hidden sm:flex gap-4">
          <Button variant="outline" onClick={() => router.push("/dashboard/properties/listings")}>Fermer</Button>
          <Button className="bg-[#fa644c] hover:bg-[#fa644c]/90 text-white" onClick={handleSave}>Enregistrer les modification</Button>
        </div>
      </div>
      {/* Mobile primary action */}
      <div className="block sm:hidden mt-4">
        <Button className="w-full bg-[#fa644c] hover:bg-[#fa644c]/90 text-white" onClick={handleSave}>Enregistrer les modification</Button>
      </div>

      {/* Mobile Dropdown for Tabs */}
      <div className="block sm:hidden mt-4 mb-6">
        <CustomSelect
          options={tabOptions}
          value={tabOptions.find((opt: { id: string; name: string }) => opt.id === activeTab)?.name || ""}
          onChange={option => setActiveTab(option.id)}
          placeholder="Choisir une section"
        />
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} defaultValue="description" className="w-full">
        {/* Desktop Tab Bar */}
        <TabsList className="hidden sm:flex border-b w-full justify-start rounded-none h-auto p-0 bg-transparent">
          <TabsTrigger value="description" className="rounded-none border-b-2 border-transparent data-[state=active]:border-[#365df5] data-[state=active]:bg-transparent px-6 py-3 text-[#64748b] data-[state=active]:text-[#365df5]">Description</TabsTrigger>
          <TabsTrigger value="personnes-chambres" className="rounded-none border-b-2 border-transparent data-[state=active]:border-[#365df5] data-[state=active]:bg-transparent px-6 py-3 text-[#64748b] data-[state=active]:text-[#365df5]">Personnes et chambres</TabsTrigger>
          <TabsTrigger value="media" className="rounded-none border-b-2 border-transparent data-[state=active]:border-[#365df5] data-[state=active]:bg-transparent px-6 py-3 text-[#64748b] data-[state=active]:text-[#365df5]">Media</TabsTrigger>
          <TabsTrigger value="location" className="rounded-none border-b-2 border-transparent data-[state=active]:border-[#365df5] data-[state=active]:bg-transparent px-6 py-3 text-[#64748b] data-[state=active]:text-[#365df5]">Location</TabsTrigger>
          <TabsTrigger value="prix" className="rounded-none border-b-2 border-transparent data-[state=active]:border-[#365df5] data-[state=active]:bg-transparent px-6 py-3 text-[#64748b] data-[state=active]:text-[#365df5]">Prix</TabsTrigger>
          <TabsTrigger value="amenities" className="rounded-none border-b-2 border-transparent data-[state=active]:border-[#365df5] data-[state=active]:bg-transparent px-6 py-3 text-[#64748b] data-[state=active]:text-[#365df5]">Équipements</TabsTrigger>
          <TabsTrigger value="regles" className="rounded-none border-b-2 border-transparent data-[state=active]:border-[#365df5] data-[state=active]:bg-transparent px-6 py-3 text-[#64748b] data-[state=active]:text-[#365df5]">Règles</TabsTrigger>
          <TabsTrigger value="annulation" className="rounded-none border-b-2 border-transparent data-[state=active]:border-[#365df5] data-[state=active]:bg-transparent px-6 py-3 text-[#64748b] data-[state=active]:text-[#365df5]">Annulation</TabsTrigger>
        </TabsList>

        <TabsContent value="description" className="mt-6">
          <DescriptionTab 
            propertyData={propertyData} 
            setPropertyData={setPropertyData}
            titleError={titleError}
            setTitleError={setTitleError}
            descriptionError={descriptionError}
            setDescriptionError={setDescriptionError}
          />
        </TabsContent>

        <TabsContent value="personnes-chambres" className="mt-6">
          <RoomsAndPeopleTab 
            propertyData={propertyData}
            setPropertyData={setPropertyData}
          />
        </TabsContent>

        <TabsContent value="media" className="mt-6">
          <MediaTab 
            propertyData={propertyData}
            setPropertyData={setPropertyData}
            pendingCoverImage={pendingCoverImage}
            setPendingCoverImage={setPendingCoverImage}
            pendingCoverImageUrl={pendingCoverImageUrl}
            setPendingCoverImageUrl={setPendingCoverImageUrl}
            pendingImages={pendingImages}
            setPendingImages={setPendingImages}
          />
        </TabsContent>

        <TabsContent value="location" className="mt-6">
          <LocationTab 
            propertyData={propertyData}
            setPropertyData={setPropertyData}
            locationErrors={locationErrors}
            setLocationErrors={setLocationErrors}
          />
        </TabsContent>

        <TabsContent value="prix" className="mt-6">
          <PricingTab 
            propertyData={propertyData}
            setPropertyData={setPropertyData}
            isFeeModalOpen={isFeeModalOpen}
            setIsFeeModalOpen={setIsFeeModalOpen}
            feeModalData={feeModalData}
            setFeeModalData={setFeeModalData}
            isTimeStayModalOpen={isTimeStayModalOpen}
            setIsTimeStayModalOpen={setIsTimeStayModalOpen}
            timeStayData={timeStayData}
            setTimeStayData={setTimeStayData}
          />
        </TabsContent>

        <TabsContent value="amenities" className="mt-6">
          <AmenitiesTab 
            propertyData={propertyData} 
            setPropertyData={setPropertyData} 
          />
        </TabsContent>

        <TabsContent value="regles" className="mt-6">
          <RulesTab 
            propertyData={propertyData} 
            setPropertyData={setPropertyData} 
          />
        </TabsContent>

        <TabsContent value="annulation" className="mt-6">
          <CancellationTab 
            propertyData={propertyData} 
            setPropertyData={setPropertyData} 
          />
        </TabsContent>
      </Tabs>

      {/* Modals */}
      <FeeModal 
        isOpen={isFeeModalOpen}
        onClose={() => setIsFeeModalOpen(false)}
        data={feeModalData}
        onSave={handleSaveFee}
      />

      <TimeStayModal
        isOpen={isTimeStayModalOpen}
        onClose={() => setIsTimeStayModalOpen(false)}
        data={timeStayData}
        onSave={handleSaveTimeStay}
      />
    </div>
  )
}
