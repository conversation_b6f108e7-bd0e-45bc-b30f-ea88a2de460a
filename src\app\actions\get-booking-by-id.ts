"use server"

import { createClient } from "@/utils/supabase/server"
import type { Booking } from "./get-bookings"

export async function getBookingById(id: string): Promise<Booking | null> {

    const supabase =await createClient()

    const {
        data: { user },
        error: userError,
    } = await supabase.auth.getUser()
    if (userError) throw new Error("Failed to get user")

    const { data: booking, error: bookingError } = await supabase
        .from("bookings")
        .select(`
      *,
      listing:listings (
        id,
        title,
        description,
        address,
        featured_image_url,
        num_guests,
        num_bedrooms,
        num_beds,
        num_bathrooms,
        listing_pricing (
          nightly_rate,
          min_stay,
          max_stay
        ),
        listing_photos (
          photo_url
        )
      )
    `)
        .eq("id", id)
        .eq("listings.host_id", user?.id)
        .single()

    if (bookingError) {
        console.error("Error fetching booking:", bookingError)
        return null
    }

    // Fetch user data separately
    const { data: userData, error: userDataError } = await supabase
        .from("profiles")
        .select("id, fullname, phone_number, avatar_url")
        .eq("id", booking.user_id)
        .single()

    if (userDataError) {
        console.error("Error fetching user data:", userDataError)
        // Continue without user data
    }

    return {
        ...booking,
        user: userData || { id: booking.user_id, fullname: "Email not available", phone_number: "number not available" },
    } as Booking
}

