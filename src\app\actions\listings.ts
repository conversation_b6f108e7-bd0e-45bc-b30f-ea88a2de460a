'use server'

import { createClient } from "@/utils/supabase/server"
import { revalidatePath } from "next/cache"

export async function approveListing(listingId: string) {
  try {
    const supabase = await createClient()
    
    // First verify the listing exists
    const { data: listing, error: fetchError } = await supabase
      .from('listings')
      .select('*')
      .eq('id', listingId)
      .single()

    if (fetchError) throw fetchError
    if (!listing) throw new Error('Listing not found')

    // Update only the status field
    const { data, error: updateError } = await supabase
      .from('listings')
      .update({ 
        status: 'active',
        // Preserve all other fields
        updated_at: new Date().toISOString()
      })
      .eq('id', listingId)
      .select()

    if (updateError) {
      console.error('Update error:', updateError)
      throw updateError
    }

    // Check for pending subscriptions for this listing and activate them
    const { data: subscriptions, error: subError } = await supabase
      .from('listing_subscriptions')
      .select('*')
      .eq('listing_id', listingId)
      .eq('is_activated', false)

    if (subError) {
      console.error('Error fetching subscriptions:', subError)
    } else if (subscriptions && subscriptions.length > 0) {
      console.log(`Found ${subscriptions.length} pending subscriptions to activate`)
      
      for (const subscription of subscriptions) {
        const now = new Date()
        const endDate = new Date()
        
        // Calculate end date based on stored duration
        if (subscription.subscription_duration_days) {
          endDate.setDate(endDate.getDate() + subscription.subscription_duration_days)
        } else {
          // Fallback - determine duration from plan name if available
          const durationDays = getDurationFromPlanName(subscription.subscription_plan)
          endDate.setDate(endDate.getDate() + durationDays)
        }

        // Update subscription
        const { error: subscriptionUpdateError } = await supabase
          .from('listing_subscriptions')
          .update({
            is_activated: true,
            activation_date: now.toISOString(),
            start_date: now.toISOString().split('T')[0],
            end_date: endDate.toISOString().split('T')[0]
          })
          .eq('id', subscription.id)

        if (subscriptionUpdateError) {
          console.error(`Error activating subscription ${subscription.id}:`, subscriptionUpdateError)
        } else {
          console.log(`Activated subscription ${subscription.id}`)
        }
      }
    }

    revalidatePath('/AdminDashboard')
    return { success: true, data }
  } catch (error) {
    console.error('Error approving listing:', error)
    return { success: false, error: 'Failed to approve listing' }
  }
}

// Helper function to determine duration from plan name if duration wasn't stored
function getDurationFromPlanName(planName: string): number {
  // Default to 30 days if can't determine
  if (!planName) return 30
  
  const planLower = planName.toLowerCase()
  
  if (planLower.includes('monthly') || planLower.includes('mensuel')) {
    return 30
  } else if (planLower.includes('yearly') || planLower.includes('annuel')) {
    return 365
  } else if (planLower.includes('six') || planLower.includes('sixmonth') || planLower.includes('semestriel')) {
    return 180
  } else {
    return 30 // Default fallback
  }
}

export async function deleteListing(listingId: string) {
  try {
    const supabase = await createClient()
    
    const { error } = await supabase
      .from('listings')
      .delete()
      .eq('id', listingId)

    if (error) throw error

    revalidatePath('/AdminDashboard')
    return { success: true }
  } catch (error) {
    console.error('Error deleting listing:', error)
    return { success: false, error: 'Failed to delete listing' }
  }
}

