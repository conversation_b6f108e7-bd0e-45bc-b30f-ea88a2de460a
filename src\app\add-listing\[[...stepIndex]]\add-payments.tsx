"use client";

import React, { useState, useEffect } from "react";
import { AlertCircle } from 'lucide-react';
import { useFormContext } from "../FormContext";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import CommonLayout from "./CommonLayout";
import { motion } from "framer-motion";
import { CheckCircle } from 'lucide-react';
import Image from "next/image";
import { createClient } from '@/utils/supabase/client';

// Imports for modularized helpers, hooks, types, and components
import { formatRIB, getPlanAmount, getPlanDisplayName } from './payments/paymentHelpers';
import { PaymentType, PaymentMethod, TrialInfo } from './payments/paymentTypes';
import { useTrial, usePaymentMethods } from './payments/paymentHooks';
import PaymentStatusSection from './payments/PaymentStatusSection';
import PaymentSetupModal from './payments/components/PaymentSetupModal';
import SubscriptionPlansModal from './payments/components/SubscriptionPlansModal';
import SecureReservationModal from './payments/components/SecureReservationModal';

// Imports for animation variants
import {
  pageVariants,
  contentSwitchVariants,
  cardListVariants,
  cardItemVariants
} from './payments/animationVariants';




const AddPayments = (): JSX.Element => {
  // Context and state for the add-listing payment step
  const { formData, setFormData, formErrors, validateStep } = useFormContext();

  // Real-time validation effect for payment fields
  useEffect(() => {
    // Debounce validation for smoother UX
    const handler = setTimeout(() => {
      // Only re-validate if there are currently errors
      if (formErrors.selectedPaymentType || formErrors.paymentMethodInfo) {
        validateStep('add-payments');
      }
    }, 150);
    return () => clearTimeout(handler);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.selectedPaymentType, formData.paymentMethodInfo]);

  const [selectedOption, setSelectedOption] = useState<string | null>("instant"); // UI selection: 'instant', 'cash', or null
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false); // Controls PaymentSetupModal
  const [isPlansModalOpen, setIsPlansModalOpen] = useState(false); // Controls SubscriptionPlansModal
  const [paymentStep, setPaymentStep] = useState<'methods' | 'bankForm' | 'd17Form' | 'success'>('methods'); // Step in PaymentSetupModal
  const [isLoading, setIsLoading] = useState<boolean>(false); // Loading state for async actions
  const [isSaving, setIsSaving] = useState<boolean>(false); // Saving state for payment method forms
  const [accountType, setAccountType] = useState<'courant' | 'epargne'>('courant'); // Bank account type
  const [accountName, setAccountName] = useState<string>('');
  const [bankName, setBankName] = useState<string>('');
  const [accountNumber, setAccountNumber] = useState<string>('');
  const [confirmAccountNumber, setConfirmAccountNumber] = useState<string>('');
  const [d17PhoneNumber, setD17PhoneNumber] = useState<string>('');
  const [confirmD17PhoneNumber, setConfirmD17PhoneNumber] = useState<string>('');
  const [d17Error, setD17Error] = useState<string>('');
  const [bankError, setBankError] = useState<string>('');
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false); // Processing state for trial/subscription
  const [isSecureModalOpen, setIsSecureModalOpen] = useState(false); // Controls SecureReservationModal
  const [hasPaymentMethod, setHasPaymentMethod] = useState<boolean>(false); // Tracks if user has a payment method
  const router = useRouter();
  const [selectedPaymentType, setSelectedPaymentType] = useState<PaymentType>(null); // Current payment type
  const { toast } = useToast();
  const [paymentCompleted, setPaymentCompleted] = useState<boolean>(false); // True if payment is finalized
  const [subscriptionDetails, setSubscriptionDetails] = useState<{
    plan: string;
    duration: number;
  } | null>(null); // Subscription plan details
  const { selectedPaymentMethod, setSelectedPaymentMethod } = usePaymentMethods();
  const { trialInfo, setTrialInfo, hasShownTrialCard, setHasShownTrialCard } = useTrial();

  // --- Effects ---

  // Sync selected payment type with formData (for backend and navigation)
  useEffect(() => {
    if (!selectedPaymentType) return;
    const currentPaymentSessionId = formData.paymentSessionId || undefined;
    setFormData(prev => {
      if (prev.selectedPaymentType === selectedPaymentType && 
          prev.paymentSessionId === currentPaymentSessionId) {
        return prev;
      }
      return {
        ...prev,
        selectedPaymentType,
        paymentSessionId: currentPaymentSessionId
      };
    });
  }, [selectedPaymentType, isPaymentModalOpen]);

  // Prevent body scroll when payment modal is open
  useEffect(() => {
    if (isPaymentModalOpen) {
      const scrollY = window.scrollY;
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = '100%';
    } else {
      const scrollY = document.body.style.top;
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      window.scrollTo(0, parseInt(scrollY || '0') * -1);
    }
    return () => {
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
    };
  }, [isPaymentModalOpen]);

  // Check for completed payment session on mount (after redirect or reload)
  useEffect(() => {
    const checkPaymentStatus = async () => {
      let sessionId = formData.paymentSessionId;
      // Fallback to localStorage if not in formData
      if (!sessionId && typeof window !== 'undefined') {
        const saved = localStorage.getItem('addListingFormData');
        if (saved) {
          try {
            const parsed = JSON.parse(saved);
            sessionId = parsed.paymentSessionId;
          } catch {}
        }
      }
      if (!sessionId) return;
      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('user_payment_sessions')
          .select('*')
          .eq('order_id', sessionId)
          .single();
        if (!error && data && data.status === 'completed' && data.payment_type === 'subscription') {
          setPaymentCompleted(true);
          setSubscriptionDetails({
            plan: data.plan_name,
            duration: data.plan_duration,
          });
        }
      } catch (err) {
        // Optionally log error
      }
    };
    checkPaymentStatus();
    // Only run on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Rehydrate local state from formData on mount or when formData changes
  useEffect(() => {
    if (formData.selectedPaymentType) {
      setSelectedPaymentType(formData.selectedPaymentType as PaymentType);
    }
    if (formData.paymentMethodInfo) {
      setSelectedPaymentMethod(formData.paymentMethodInfo.type);
    }
    // Rehydrate trialInfo from formData if present and valid
    const maybeTrialInfo = (formData as any).trialInfo;
    if (
      maybeTrialInfo &&
      typeof maybeTrialInfo === 'object' &&
      typeof maybeTrialInfo.startDate === 'string' &&
      typeof maybeTrialInfo.endDate === 'string'
    ) {
      setTrialInfo(maybeTrialInfo);
    } else {
      setTrialInfo(undefined); // <-- Ensure trialInfo is cleared if not present in formData
    }
  }, [formData]);

  // Keep trialInfo in sync with formData.trialInfo
  useEffect(() => {
    if (!formData.trialInfo && trialInfo) {
      setTrialInfo(undefined);
    }
  }, [formData.trialInfo]);

  // Synchronize selectedOption with persisted payment type and trial status
  useEffect(() => {
    if (formData.selectedPaymentType === 'trial' && trialInfo) {
      setSelectedOption('cash');
    } else if (formData.selectedPaymentType === 'secure') {
      setSelectedOption('instant');
    } else if (formData.selectedPaymentType === 'cash' || formData.selectedPaymentType === 'subscription') {
      setSelectedOption('cash');
    } else {
      setSelectedOption('null');
    }
  }, [formData.selectedPaymentType, trialInfo]);

  // --- Handlers and Business Logic ---

  // Handle user selecting a reservation option (secure or flexible)
  const handleOptionChange = (option: string) => {
    if (paymentCompleted) {
      toast({
        variant: "destructive",
        title: "Modification impossible",
        description: "Votre paiement a déjà été complété. Les paramètres de paiement ne peuvent plus être modifiés."
      });
      return;
    }
    if (option === "instant") {
      openSecureModal();
    } else if (option === "cash") {
      openPlansModal();
    }
  };

  // Close PaymentSetupModal
  const closePaymentModal = () => {
    setIsPaymentModalOpen(false);
  };

  // Close SubscriptionPlansModal
  const closePlansModal = () => {
    setIsPlansModalOpen(false);
  };

  // Handle user selecting a payment method (bank, d17)
  const handlePaymentMethodSelect = (method: string) => {
    if (paymentCompleted) {
      toast({
        variant: "destructive",
        title: "Modification impossible",
        description: "Votre paiement a déjà été complété. Les paramètres de paiement ne peuvent plus être modifiés."
      });
      return;
    }
    if (method === 'bank' || method === 'd17') {
      setSelectedPaymentMethod(method as PaymentMethod);
      setPaymentStep(method === 'bank' ? 'bankForm' : 'd17Form');
      setBankError("");
    }
  };

  // Handle subscription purchase (calls backend, redirects to payment gateway)
  const handleSubscriptionPurchase = async (plan: string, duration: number) => {
    if (paymentCompleted) {
      toast({
        variant: "destructive",
        title: "Modification impossible",
        description: "Votre paiement a déjà été complété. Les paramètres de paiement ne peuvent plus être modifiés."
      });
      return;
    }
    try {
      setIsLoading(true);
      // Call backend to initialize payment
      const response = await fetch('/api/init-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          plan, 
          duration,
          isSubscription: true,
          subscriptionPlan: plan,
          subscriptionDuration: duration,
          amount: getPlanAmount(plan),
          description: `Abonnement ${getPlanDisplayName(plan)}` 
        }),
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to initialize payment: ${response.status} ${errorText}`);
      }
      const data = await response.json();
      setFormData(prev => ({
        ...prev,
        paymentSessionId: data.orderId || undefined,
        selectedPaymentType: 'subscription',
        trialInfo: undefined,
        paymentMethodInfo: undefined,
        paymentModel: undefined,
      }));
      // Set flag to suppress restore modal on payment return
      if (typeof window !== 'undefined') {
        localStorage.setItem('addListingReturningFromPayment', 'true');
      }
      window.location.href = data.paymentUrl;
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Une erreur est survenue lors de l'initialisation du paiement"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Save bank account info and update formData
  const handleSaveBankAccount = () => {
    // Validate fields and update formData with bank info
    if (!accountType) {
      setBankError("Veuillez sélectionner un type de compte bancaire.");
      return;
    }
    if (!accountName.trim()) {
      setBankError("Veuillez saisir le nom du titulaire du compte.");
      return;
    }
    if (!bankName.trim()) {
      setBankError("Veuillez saisir le nom de la banque.");
      return;
    }
    if (!accountNumber.trim() || accountNumber.replace(/\s/g, '').length < 20) {
      setBankError("Veuillez saisir un RIB valide (20 chiffres).");
      return;
    }
    if (accountNumber !== confirmAccountNumber) {
      setBankError("Les deux RIB ne correspondent pas. Veuillez vérifier.");
      return;
    }
    const paymentMethodInfo = {
      type: 'bank',
      data: {
        account_type: accountType,
        account_name: accountName.trim(),
        bank_name: bankName.trim(),
        account_number: accountNumber.replace(/\s/g, ''),
        bank_state: 'Tunisia'
      }
    };
    setFormData(prev => ({
      ...prev,
      paymentMethodInfo: paymentMethodInfo,
      selectedPaymentType: 'secure',
      paymentModel: 'commission',
      paymentSessionId: undefined,
      trialInfo: undefined, // Ensure trialInfo is cleared in formData
    }));
    setSelectedPaymentType('secure');
    setSelectedPaymentMethod('bank');
    setTrialInfo(undefined); // <-- Clear trialInfo React state
    closePaymentModal();
    toast({
      title: "Informations bancaires enregistrées",
      description: "Vos informations de compte bancaire ont été enregistrées avec succès."
    });
  };

  // Save D17 info and update formData
  const handleSaveD17 = () => {
    if (!d17PhoneNumber.trim() || d17PhoneNumber.length !== 8) {
      setD17Error("Veuillez saisir un numéro de téléphone D17 valide (8 chiffres).");
      return;
    }
    if (d17PhoneNumber !== confirmD17PhoneNumber) {
      setD17Error("Les deux numéros ne correspondent pas. Veuillez vérifier.");
      return;
    }
    const paymentMethodInfo = {
      type: 'd17',
      data: {
        d17_phone_number: d17PhoneNumber.trim()
      }
    };
    setFormData(prev => ({
      ...prev,
      paymentMethodInfo: paymentMethodInfo,
      selectedPaymentType: 'secure',
      paymentModel: 'commission',
      paymentSessionId: undefined,
      trialInfo: undefined, // Ensure trialInfo is cleared in formData
    }));
    setSelectedPaymentType('secure');
    setSelectedPaymentMethod('d17');
    setTrialInfo(undefined); // <-- Clear trialInfo React state
    closePaymentModal();
    toast({
      title: "Informations D17 enregistrées",
      description: "Votre numéro de téléphone D17 a été enregistré avec succès."
    });
  };

  // Open SecureReservationModal (with payment completed guard)
  const openSecureModal = () => {
    if (paymentCompleted) {
      toast({
        variant: "destructive",
        title: "Modification impossible",
        description: "Votre paiement a déjà été complété. Les paramètres de paiement ne peuvent plus être modifiés."
      });
      return;
    }
    setIsSecureModalOpen(true);
    setSelectedOption("instant");
  };

  // Close SecureReservationModal
  const closeSecureModal = () => {
    setIsSecureModalOpen(false);
  };

  // Open SubscriptionPlansModal (with payment completed guard)
  const openPlansModal = () => {
    if (paymentCompleted) {
      toast({
        variant: "destructive",
        title: "Modification impossible",
        description: "Votre paiement a déjà été complété. Les paramètres de paiement ne peuvent plus être modifiés."
      });
      return;
    }
    setIsPlansModalOpen(true);
    setSelectedOption("cash");
  };

  // Format RIB input with spaces for readability
  const handleRIBInput = (value: string, setter: (value: string) => void) => {
    const digits = value.replace(/\D/g, '');
    if (digits.length <= 20) {
      setter(formatRIB(digits));
    }
  };

  // Start a free trial (sets trial info and closes modals)
  const handleTrialStart = () => {
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 60);
    const trialData = {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    };
    setFormData(prev => {
      const { paymentMethodInfo, paymentModel, ...rest } = prev;
      return {
        ...rest,
        selectedPaymentType: 'trial',
        trialInfo: trialData,
        paymentMethodInfo: undefined,
        paymentModel: undefined,
        paymentSessionId: undefined,
      };
    });
    setSelectedPaymentType('trial');
    setSelectedPaymentMethod(null);
    setTrialInfo(trialData);
    closePaymentModal();
    closePlansModal();
    toast({
      title: "Période d'essai activée",
      description: "Votre annonce sera en période d'essai pendant 60 jours."
    });
  };

  // Track if the trial card has been shown (for animation)
  useEffect(() => {
    if (selectedPaymentType === 'trial' && trialInfo && !hasShownTrialCard) {
      setHasShownTrialCard(true);
    }
  }, [selectedPaymentType, trialInfo, hasShownTrialCard]);

  return (
    <CommonLayout params={{ stepIndex: 'add-payments' }}>
      {/* Error Feedback: Payment validation */}
      {(formErrors?.selectedPaymentType || formErrors?.paymentMethodInfo) && (
        <div
          className="flex items-center gap-2 mb-4 p-3 rounded bg-red-50 border border-red-200 text-red-700"
          role="alert"
          aria-live="assertive"
        >
          <AlertCircle className="w-5 h-5 text-red-500" />
          <span>
            {formErrors.selectedPaymentType || formErrors.paymentMethodInfo}
          </span>
        </div>
      )}
      <motion.div 
        className="max-w-3xl mx-auto px-4 py-16 flex flex-col space-y-8"
        variants={pageVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Only show PaymentStatusSection if not in completed subscription state */}
        {(!paymentCompleted || !subscriptionDetails) && (
          (formData.selectedPaymentType === 'trial' && trialInfo) ||
          formData.selectedPaymentType === 'secure' ||
          formData.selectedPaymentType === 'cash'
        ) && (
          <PaymentStatusSection
            paymentType={formData.selectedPaymentType as PaymentType}
            paymentMethod={formData.paymentMethodInfo?.type}
            trialInfo={trialInfo}
            onModify={() => {
              if (paymentCompleted) {
                toast({
                  variant: "destructive",
                  title: "Modification impossible",
                  description: "Votre paiement a déjà été complété. Les paramètres de paiement ne peuvent plus être modifiés."
                });
              } else if (formData.selectedPaymentType === 'trial') {
                setIsPlansModalOpen(true);
                setSelectedOption('cash');
              } else if (selectedOption === "instant" || formData.selectedPaymentType === 'secure') {
                setIsSecureModalOpen(true);
                setSelectedOption("instant");
              } else if (selectedOption === "cash" || formData.selectedPaymentType === 'cash' || formData.selectedPaymentType === 'subscription') {
                setIsPlansModalOpen(true);
                setSelectedOption("cash");
              }
            }}
            animateOnMount={formData.selectedPaymentType === 'trial' && !hasShownTrialCard}
            paymentCompleted={paymentCompleted}
            subscriptionDetails={subscriptionDetails}
          />
        )}

            <motion.div
              variants={contentSwitchVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
            >
          {paymentCompleted && subscriptionDetails ? (
              <>
                <h1 className="text-2xl font-semibold text-[#1A1A1A]">
                  Votre abonnement est actif – Vous êtes prêt à publier votre annonce !
                </h1>
                <p className="text-neutral-500 dark:text-neutral-400">
                  Félicitations, votre abonnement a été activé avec succès. Vous pouvez maintenant publier votre annonce. Les paramètres de paiement ne peuvent plus être modifiés.
                </p>

                <div className="bg-orange-50 border border-orange-200 rounded p-6 my-8">
                  <div className="flex items-center space-x-3 mb-4">
                    <CheckCircle className="h-6 w-6 text-orange-500" />
                    <h2 className="text-xl font-semibold text-[#1A1A1A]">Paiement Complété</h2>
                  </div>
                  
                  <p className="text-gray-700 mb-4">
                    Vous avez souscrit avec succès à un abonnement <span className="font-medium">{getPlanDisplayName(subscriptionDetails.plan)}</span>. 
                    Votre compte a été débité et votre abonnement est maintenant actif.
                  </p>
                  
                  <div className="bg-white border border-gray-100 rounded p-4 mb-6">
                    <h3 className="font-medium mb-2">Détails de votre abonnement:</h3>
                    <ul className="space-y-2 text-gray-700">
                      <li className="flex justify-between">
                        <span>Plan:</span> 
                        <span className="font-medium">{getPlanDisplayName(subscriptionDetails.plan)}</span>
                      </li>
                      <li className="flex justify-between">
                        <span>Durée:</span> 
                        <span className="font-medium">{subscriptionDetails.duration} jours</span>
                      </li>
                      <li className="flex justify-between">
                        <span>Type de réservation:</span>
                        <span className="font-medium">Réservation Flexible</span>
                      </li>
                      <li className="flex justify-between">
                        <span>Date de début:</span>
                        <span className="font-medium">{new Date().toLocaleDateString()}</span>
                      </li>
                      <li className="flex justify-between">
                        <span>Date de fin:</span>
                        {subscriptionDetails.duration && (
                          <span className="font-medium">
                            {new Date(Date.now() + subscriptionDetails.duration * 24 * 60 * 60 * 1000).toLocaleDateString()}
                          </span>
                        )}
                      </li>
                    </ul>
                  </div>
                  
                  <div className="bg-[#EA580F26] border border-orange-200 rounded p-4">
                    <p className="text-sm text-[#1A1A1A]">
                      <span className="font-medium">Félicitations !</span> Votre abonnement est actif. Vous pouvez maintenant publier votre annonce. Lors de la publication, votre abonnement sera automatiquement associé à votre annonce et vous bénéficierez de tous les avantages de votre plan.
                    </p>
                  </div>
                </div>
              </>
          ) : (
              <>
                <h1 className="text-2xl font-semibold text-[#1A1A1A]">Choisissez vos paramètres de réservation</h1>
                <p className="text-neutral-500 dark:text-neutral-400">Vous pouvez modifier ce paramètre à tout moment.</p>

                <motion.div 
                className="grid gap-6 mt-8"
                  variants={cardListVariants}
                initial="hidden"
                  animate="visible"
                >
                  {/* Instant Reservation Option */}
                  <motion.div
                    variants={cardItemVariants}
                    className={`
                      relative rounded border-2 p-5 transition-all max-w-3xl ${paymentCompleted ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:shadow-md hover:translate-y-[-2px]'}
                      ${(selectedOption === 'instant') ? 'border-orange-500 bg-orange-50/50' : 'border-gray-200 hover:border-gray-300'}
                    `}
                    onClick={() => {
                      if (paymentCompleted) {
                        toast({
                          variant: "destructive",
                          title: "Modification impossible",
                          description: "Votre paiement a déjà été complété. Les paramètres de paiement ne peuvent plus être modifiés."
                        });
                      } else {
                        handleOptionChange('instant');
                      }
                    }}
                  >
                    <div className="flex items-start justify-between">
                      {/* Left side with content */}
                      <div className="flex-grow space-y-3 max-w-[85%]">
                        <div>
                        <div className="flex items-start">
                          <h3 className="text-lg font-semibold text-[#1A1A1A]">Paiement en ligne</h3>
                          <span className="inline-block ml-2 px-2 py-0.5 bg-orange-500 text-white text-xs font-semibold rounded-full align-super" style={{ marginTop: '-2px' }}>
                            Recommandé
                          </span>
                        </div>
                        
                        <ul className="space-y-2 text-gray-600">
                          <li className="flex items-start">
                            <span className="text-gray-700 mr-2">•</span>
                            <span className="text-sm">Les utilisateurs réservent instantanément après paiement, et les hôtes perçoivent une commission de <span className="font-medium">5%</span> déduite automatiquement du montant total de chaque réservation confirmée.</span>
                          </li>
                        </ul>
                      </div>
                      </div>

                      {/* Right side with illustration */}
                      <div className="flex-shrink-0 w-16 h-16 relative">
                        <Image
                          src="https://api.almindharbooking.com/storage/v1/object/public/add-listing//add_listing_thunder.svg"
                          alt="Réservation instantanée"
                          width={64}
                          height={64}
                          className="object-contain"
                        />
                        {selectedOption === 'instant' && (
                          <div className="absolute -top-1 -right-1 w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>

                  {/* Cash-based Reservation Option */}
                  <motion.div
                    variants={cardItemVariants}
                    className={`
                      relative rounded border-2 p-5 transition-all max-w-3xl ${paymentCompleted ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:shadow-md hover:translate-y-[-2px]'}
                      ${(selectedOption === 'cash') ? 'border-[#16214F] bg-[#16214F] text-white' : 'border-gray-200 hover:border-gray-300'}
                    `}
                    onClick={() => {
                      if (paymentCompleted) {
                        toast({
                          variant: "destructive",
                          title: "Modification impossible",
                          description: "Votre paiement a déjà été complété. Les paramètres de paiement ne peuvent plus être modifiés."
                        });
                      } else {
                        handleOptionChange('cash');
                      }
                    }}
                  >
                    <div className="flex items-start justify-between">
                      {/* Left side with content */}
                      <div className="flex-grow space-y-3 max-w-[85%]">
                        <h3 className={`text-lg font-semibold ${selectedOption === 'cash' ? 'text-white' : 'text-[#1A1A1A]'}`}>Paiement à l&apos;arrivée</h3>

                        <ul className={`space-y-2 ${selectedOption === 'cash' ? 'text-white' : 'text-gray-600'}`}>
                          <li className="flex items-start">
                            <span className={`${selectedOption === 'cash' ? 'text-white' : 'text-gray-700'} mr-2`}>•</span>
                            <span className="text-sm">Aucune commission, aucun frais caché: seulement un abonnement simple et transparent. Recevez vos réservations, gérez votre calendrier en toute flexibilité, accueillez vos invités et gagnez de l&apos;argent.</span>
                          </li>
                        </ul>
                      </div>

                      {/* Right side with illustration */}
                      <div className="flex-shrink-0 w-16 h-16 relative">
                        <div className={`${selectedOption === 'cash' ? 'p-1.5 bg-white rounded-full' : ''}`}>
                          <Image
                            src="https://api.almindharbooking.com/storage/v1/object/public/add-listing//add_listing_cash.svg"
                            alt="Réservation en espèces"
                            width={64}
                            height={64}
                            className="object-contain"
                          />
                        </div>
                        {selectedOption === 'cash' && (
                          <div className="absolute -top-1 -right-1 w-5 h-5 bg-[#16214F] border-2 border-white rounded-full flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              </>
          )}
                          </motion.div>

        {/* Payment Setup Modal */}
        <PaymentSetupModal
          isOpen={isPaymentModalOpen}
          onClose={closePaymentModal}
          paymentStep={paymentStep}
          selectedPaymentMethod={selectedPaymentMethod}
          setSelectedPaymentMethod={(method: string) => setSelectedPaymentMethod(method as PaymentMethod)}
          hasPaymentMethod={hasPaymentMethod}
          accountType={accountType}
          setAccountType={setAccountType}
          accountName={accountName}
          setAccountName={setAccountName}
          bankName={bankName}
          setBankName={setBankName}
          accountNumber={accountNumber}
          setAccountNumber={setAccountNumber}
          confirmAccountNumber={confirmAccountNumber}
          setConfirmAccountNumber={setConfirmAccountNumber}
          d17PhoneNumber={d17PhoneNumber}
          setD17PhoneNumber={setD17PhoneNumber}
          confirmD17PhoneNumber={confirmD17PhoneNumber}
          setConfirmD17PhoneNumber={setConfirmD17PhoneNumber}
          d17Error={d17Error}
          setD17Error={setD17Error}
          bankError={bankError}
          setBankError={setBankError}
          isSaving={isSaving}
          handlePaymentMethodSelect={handlePaymentMethodSelect}
          handleSaveBankAccount={handleSaveBankAccount}
          handleSaveD17={handleSaveD17}
          handleRIBInput={handleRIBInput}
          successMessage={successMessage}
          setPaymentStep={setPaymentStep}
        />

        {/* Subscription Plans Modal */}
        <SubscriptionPlansModal
          isOpen={isPlansModalOpen}
          onClose={closePlansModal}
          isProcessing={isProcessing}
          handleTrialStart={handleTrialStart}
          handleSubscriptionPurchase={handleSubscriptionPurchase}
        />

        {/* Secure Reservation Modal */}
        <SecureReservationModal
          isOpen={isSecureModalOpen}
          onClose={closeSecureModal}
          hasPaymentMethod={hasPaymentMethod}
          setIsPaymentModalOpen={setIsPaymentModalOpen}
        />
      </motion.div>
    </CommonLayout>
  );
};

export default AddPayments;

